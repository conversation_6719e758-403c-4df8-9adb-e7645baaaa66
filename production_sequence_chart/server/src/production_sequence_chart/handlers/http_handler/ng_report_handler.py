# -*- coding: utf-8 -*-
# @Time : 4/13/22 4:55 PM
# <AUTHOR> root
# @Email : <EMAIL>
# @File : ng_report_handler.py
# @Project : production_sequence_chart

from datetime import datetime, timedelta

from mg_app_framework import <PERSON>ttp<PERSON>asi<PERSON><PERSON><PERSON><PERSON>, rolerequired, <PERSON>s<PERSON><PERSON>, get_logger, get_context

from production_sequence_chart.config import IDI_COLLECTION_NAME
from production_sequence_chart.util.const import *
from production_sequence_chart.util.query_util import modify_and_update_cl_data
from production_sequence_chart.util.data_util import send_http_post_request


class ModifyNGDataHandler(HttpBasicHandler):

    @rolerequired
    async def post_process(self, *args, **kwargs):
        '''
        机加车间不良上报数据校正修改
        :param args:
        :param kwargs: {"device": "device_code", "data":[{timestamp": "2021-04-24 11:22:55", "value": 10, "index":"ng_index_code"}]}
        :return:
        '''
        data = {}
        date_format = "%Y-%m-%d %H:%M:%S"
        user_name = self.operator or "root"
        device_code = self.data.get("device")
        try:
            get_logger().info(f"ModifyNGDataHandler params: {self.data.get('data')}")

            for i in self.data.get("data"):
                params = {"device": device_code, "user": user_name}
                params.update(i)
                await modify_and_update_cl_data(IDI_COLLECTION_NAME, params=params)
            self.send_response_data(MesCode.success, '', '成功')
        except Exception as e:
            get_logger().error(f"ModifyNGDataHandler post error: {e}")
            self.send_response_data(MesCode.fail, 'fail', f'{e}')

        start_date = sorted(self.data.get("data"), key=lambda x:x["timestamp"])[0]["timestamp"]
        end_date = datetime.now()
        sequence_ng_data = {"device": [device_code], "start_time": start_date,
                            "end_time": end_date.strftime(date_format)}
        await send_http_post_request("http://localhost:25670/api/aat_machine_report/rerun_sequence_ng",
                                     sequence_ng_data)


class NGCFGDataHandler(HttpBasicHandler):

    async def post_process(self, *args, **kwargs):
        '''
        设备信息不良指标
        :param args:
        :param kwargs: {"device_code": "dev_0001"}
        :return:{"name":"workshop-unit-device",
                 "ng_index":[{"code":"", "label":""}]
        '''
        date_format = "%Y-%m-%d %H:%M:%S"
        device_code = self.data.get("device_code")
        data = {}
        now = datetime.now()
        target_time1 = datetime(now.year, now.month, now.day) + timedelta(hours=8)
        target_time2 = datetime(now.year, now.month, now.day) + timedelta(hours=20)

        if now <= target_time1:
            data.update({"bc_range": [(target_time2 + timedelta(days=-1)).strftime(date_format),
                                      target_time1.strftime(date_format)]})
        elif target_time1 < now <= target_time2:
            data.update({"bc_range": [target_time1.strftime(date_format), target_time2.strftime(date_format)]})
        else:
            data.update({"bc_range": [target_time2.strftime(date_format),
                                      (target_time1 + timedelta(days=1)).strftime(date_format)]})
        try:
            device_info_mapper = get_context("device_info_mapper").get(device_code, {})
            data.update({
                "name": f"{device_info_mapper.get('车间名称')}-{device_info_mapper.get('生产单元名称')}-{device_info_mapper.get('设备名称')}",
                "ng_index": ng_index_list,
                })
            get_logger().info(f"NGCFGDataHandler params: {data}")
            self.send_response_data(MesCode.success, data, '成功')
        except Exception as e:
            get_logger().error(f"NGCFGDataHandler post error: {e}")
            self.send_response_data(MesCode.fail, 'fail', f'{e}')

