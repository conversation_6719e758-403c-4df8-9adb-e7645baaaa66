from mg_app_framework.components.rabbitmq_connector import RabbitMQConfigBasic
from mg_app_framework import get_logger, get_handler, TaskKey, update_context, get_context,idi_createTable,rabbitmq_get_subscribe_message_cache
from mg_app_framework import InitFuncBasic

from production_sequence_chart.core.quality_rapid_response import init_quality_rapid_dict
from production_sequence_chart.util.query_util import (update_rapid_dict, find_rapid_dict)
from production_sequence_chart.core.mq_process import process_organization_user_info
from production_sequence_chart.util.ast_utils import (get_special_point_cl_lsit)
from production_sequence_chart.config import (production_sequence_chart_data_collection, iot_data_msg_key,
                                              production_calendar_start_time_config_key, jp_rapid_config_key,
                                              default_min_break, jp_value_realtime_key, RAPID_RESPONSE_OTHER_CONFIG_MQ,
                                              RAPID_RESPONSE_TIME_RANGE_LIST_MQ)
from datetime import datetime
from itertools import groupby
import traceback

IDI_INDEX = [("device_code", 1), ("timestamp", 1), ("value", 1)]

formula_msg = {}


async def rabbitmq_message_func(key, data, org=None):
    if key == "general_kpi_list":
        update_context("general_kpi_list", data)
        update_context("general_kpi_dict", {i["code"]: i for i in data})
    elif key == "device_kpi_list":
        update_context("device_kpi_list", data)
        update_context("device_kpi_dict", {i["code"]: i for i in data})
    elif key == "device_special_formula":
        device_special_formula_dict = {}
        for k,v in data.items():
            device_code,compute_code = k.split("~")
            device_special_formula_dict[(device_code,compute_code)] = v
        update_context("device_special_formula_dict", device_special_formula_dict)
        get_logger().info("device_special_formula_dict~~~~~~~~~~~~~~~~:%s", data)
        get_special_point_cl_lsit()
    elif key == "device_mb_formula":
        doubel_code_formula_dict = data
        update_context("doubel_code_formula_dict", doubel_code_formula_dict)
        get_logger().info("doubel_code_formula_dict~~~~~~~~~~~~~~~~:%s", doubel_code_formula_dict)
    elif key == "formula_ng_point_map":
        update_context("device_ng_point_map", data)
        get_logger().info("device_ng_point_map~~~~~~~~~~~~~~~~:%s", data)
    elif key == "WebLayout_produce_unit_import_application_used":
        await load_workshop_device_config_data(data)
    elif key == jp_rapid_config_key:
        jp_rapid_config_dict = {}
        quality_config_dict = {}
        for k, value_dict in data.items():
            jp_rapid_config_dict[k] = value_dict["beat"]["1"]
            if value_dict["quality"] is not None:
                quality_config_dict[k] = value_dict["quality"]
        # 只针对检测设备触发质量中断，找到所有的检测设备
        detection_device_list = list()
        for device_code, config_dict in quality_config_dict.items():
            if config_dict is not None:
                detection_device_list.append(device_code)
        update_context("jp_rapid_config_dict", jp_rapid_config_dict)
        update_context("quality_config_dict", quality_config_dict)
        update_context("detection_device_list", detection_device_list)
        get_logger().info("jp_rapid_config_key===========%s",jp_rapid_config_dict)
        get_logger().info("quality_config_dict===========%s", quality_config_dict)
        get_logger().info("detection_device_list===========%s", detection_device_list)
    elif key == jp_value_realtime_key:
        await update_jp_and_check_rapid(data)
    elif key == "organization_user_info":
        await process_organization_user_info(org, data)
    elif key == production_calendar_start_time_config_key:
        start_time = data["start_time"]
        try:
            time_list = start_time.split(":")
            hour = int(time_list[0])
            minute = int(time_list[1])
            start_time_config = hour * 60 + minute
            update_context("start_time_config", start_time_config)
            update_context("start_time", start_time)
            get_logger().info("start_time_config===============%s",start_time_config)
        except Exception as e:
            get_logger().error("get start time config error:%s",e)
    elif key == "production_calendar_device_shift_config":
        update_device_shift_config(data)
    elif key == RAPID_RESPONSE_OTHER_CONFIG_MQ:
        try:
            get_logger().info("key[{}] received data[{}]".format(key, data))
            update_context("cl_num_denominator", data["cl_num_denominator"])  # 计算质量快反的基数（取多少个产品计算），单位是个
            update_context("time_threshold", data["time_threshold"])  # 质量快反事件触发的事件阈值，单位是分钟
            get_logger().info(
                "MQ successfully set cl_num_denominator: {} and time_threshold: {}".format(
                    data["cl_num_denominator"], data["time_threshold"]
                )
            )
        except Exception as e:
            get_logger().error("get quality rapid response config error:%s", e)
    elif key == RAPID_RESPONSE_TIME_RANGE_LIST_MQ:
        try:
            get_logger().info("RAPID_RESPONSE_TIME_RANGE_LIST_MQ received data {}".format(data))
            response_time_range = [
                {
                    "start_time": datetime.strptime(single_item["start_time"], "%H:%M"),
                    "end_time": datetime.strptime(single_item["end_time"], "%H:%M"),
                } for single_item in data
            ]
            update_context("response_time_range", response_time_range)
        except Exception as e:
            get_logger().error("get response_time_range config error:%s", e)


# 实时判断是否触发快反
async def update_jp_and_check_rapid(data):
    from production_sequence_chart.core.jp_rapid_process import (check_if_rapid_single)
    try:
        jp_value_timestamp_dict = get_context("jp_value_timestamp_dict")
        device_code,timestamp,value = data["device_code"],data["timestamp"],data["value"]
        # 实时判断是否触发快反
        if device_code in jp_value_timestamp_dict:
            last_data_dict = jp_value_timestamp_dict[device_code]
            await check_if_rapid_single(device_code,last_data_dict,timestamp)
        jp_value_timestamp_dict[device_code] = {
            "timestamp": timestamp,
            "value": value,
        }
        update_context("jp_value_timestamp_dict", jp_value_timestamp_dict)
        # await update_rapid_dict(jp_value_timestamp_dict)  # 超级频繁写库
    except Exception as e:
        get_logger().error("update_jp_value_timestamp_dict error:%s", e)


async def load_workshop_device_config_data(data):
    try:
        data = sorted(data, key=lambda x: (x["车间代号"], x["生产单元代号"], x["设备顺序"]))
        unit_device_map_dict = {}
        workshop_device_dict = {}
        unit_device_dict = {}
        detect_device_list = []
        device_list_all = []
        workshop_list = []
        workshop_ng_list = []
        unit_name_map_dict = {}
        device_unit_mapper = {}
        device_info_mapper = {}
        device_jp_point = {}
        device_tree = []
        nodes = []
        for workshop_code, workshop_group in groupby(data,key=lambda x:x["车间代号"]):
            workshop_group = list(workshop_group)
            workshop_item = {
                "label": workshop_group[0]["车间名称"],
                "value": workshop_code,
                "pipeline_list":[]
            }
            workshop_ng_item = {
                "label": workshop_group[0]["车间名称"],
                "value": workshop_code,
                "pipeline_list":[]
            }
            workshop_device_tree = {
                "label": workshop_group[0]["车间名称"],
                "code": workshop_code,
                "children": []
            }
            for unit_code,unit_group in groupby(workshop_group,key=lambda x:x["生产单元代号"]):
                unit_group = list(unit_group)
                tmp_device_list = []
                pipeline_item = {
                    "label": unit_group[0]["生产单元名称"],
                    "value": unit_code,
                    "device_list": [],
                }
                pipeline_ng_item = {
                    "label": unit_group[0]["生产单元名称"],
                    "value": unit_code,
                    "device_list": [],
                }
                pipeline_device_tree = {
                    "label": unit_group[0]["生产单元名称"],
                    "code": unit_code,
                    "children": [],
                }
                for device_item in unit_group:
                    device_code = device_item["设备编码"]
                    device_name = device_item["设备名称"]
                    device_sort = device_item["设备顺序"]
                    device_type = device_item["设备类型"]
                    jp_point = device_item.get("节拍点位") or ""
                    device_jp_point[device_code] = jp_point
                    if device_type in ("检测", "加工"):  # 去除 人工类型设备
                        device_list_all.append({"value": device_code, "label": device_name,"seq":device_sort})
                        tmp_device_list.append({"value": device_code, "label": device_name, "seq": device_sort, "type": device_type})
                        device_unit_mapper.update({device_code: unit_code})
                        device_info_mapper.update({device_code: device_item})
                        if device_type == '检测':
                            nodes.append({'code': device_code, 'label': device_name})
                            detect_device_list.append(device_code)
                try:
                    tmp_device_list = sorted(tmp_device_list,key=lambda x: float(x.get("seq", 100)))
                except:
                    pass
                nodes.append({"code": unit_code, "label": unit_group[0]["生产单元名称"]})
                pipeline_item["device_list"] = tmp_device_list
                unit_device_map_dict[unit_code] = tmp_device_list
                pipeline_ng_item["device_list"] = [i for i in tmp_device_list if i["type"] == "检测"]
                pipeline_device_tree["children"] = [{"code": i['value'], "label": i['label']} for i in tmp_device_list if i["type"] == "检测"]
                workshop_item["pipeline_list"].append(pipeline_item)
                workshop_ng_item['pipeline_list'].append(pipeline_ng_item)
                workshop_device_tree["children"].append(pipeline_device_tree)
                unit_device_dict[unit_code] = [i["value"] for i in tmp_device_list if i["type"] == "检测"]
            workshop_device_dict[workshop_code] = [i["设备编码"] for i in workshop_group if i["设备类型"] == "检测"]
            workshop_list.append(workshop_item)
            workshop_ng_list.append(workshop_ng_item)
            device_tree.append(workshop_device_tree)
            nodes.append({"code": workshop_code, "label": workshop_group[0]["车间名称"]})
        device_code_name_dict = {i["value"]: i["label"] for i in device_list_all}
        update_context("device_code_name_dict", device_code_name_dict)
        update_context("unit_device_map_dict", unit_device_map_dict)
        update_context("device_list_all", device_list_all)
        get_logger().info("unit_device_map_dict=========:%s", unit_device_map_dict)
        get_logger().info("device_list_all==================%s", device_list_all)
        #
        update_context("workshop_list", workshop_list)
        update_context("workshop_ng_list", workshop_ng_list)
        update_context("unit_name_map_dict", unit_name_map_dict)
        update_context("device_unit_mapper", device_unit_mapper)
        update_context("device_info_mapper", device_info_mapper)
        #
        update_context("device_tree", device_tree)
        update_context("tree_nodes", nodes)

        update_context("workshop_device_dict", workshop_device_dict)
        update_context("unit_device_dict", unit_device_dict)
        update_context("detect_device_list", detect_device_list)
        get_logger().info("workshop_list====================:%s", workshop_list)
        # get_logger().info("workshop_unit_map_dict===========:%s", workshop_unit_map_dict)
        device_find_unit_and_workshop_name = {
            x["设备编码"]: {
                "车间名称": x["车间名称"],
                "生产单元名称": x["生产单元名称"],
                "设备名称": x["设备名称"]
            } for x in data
        }
        update_context("device_find_unit_and_workshop_name", device_find_unit_and_workshop_name)
        get_logger().info("device_find_unit_and_workshop_name: {}".format(device_find_unit_and_workshop_name))

        update_context("device_jp_point", device_jp_point)
        get_logger().info("device_jp_point====================:%s", device_jp_point)

    except Exception as e:
        traceback.print_exc()
        get_logger().error("load_workshop_device_config_data error:%s",e)


def update_device_shift_config(data):
    """
    :param data: {'dev_smt09_syj': [{'title': '早班', 'time': ['09:00', '21:00', 0, 0], 'isWork': True}, {'title': '晚班', 'time': ['22:00', '05:59', 0, 1], 'isWork': True}],
                  'dev_smt09_spi': [{'title': '早班', 'time': ['09:00', '21:00', 0, 0], 'isWork': True}, {'title': '晚班', 'time': ['22:00', '05:59', 0, 1], 'isWork': True}]}
    拿到生产日历设置的上班时间范围，精确到设备
    devices_work_time数据结构
    {
        device_code_A: [
            (t1, t2, 0, 0, 1)  # 第一个班次的范围(t1, t2)
            (t3, t4, 0, 1, 2)  # 第二个班次的范围(t3, t4)
            (t5, t6, 1, 1, 3)  # 第三个班次的范围(t5, t6)
        ]
    }
    """
    try:
        from datetime import datetime
        get_logger().info('get_calendar_work_time received raw msg: {}'.format(data))
        devices_work_time = dict()
        for device_code, v_list in data.items():
            if type(v_list) != list:
                continue
            tmp_work_time_list = list()
            for i, x in enumerate(v_list):  # 遍历所有的班次信息，拿出所有的上班时间
                if x.get("isWork", None):  # 只解析上班的班次
                    t_start = datetime.strptime(x["time"][0], "%H:%M")
                    t_end = datetime.strptime(x["time"][1], "%H:%M")
                    tmp_work_time_list.append(
                        (t_start, t_end, x["time"][2], x["time"][3], i + 1)  # time字段 最后一位字段只会是0或者1，0表示今天，1表示明天
                    )
            devices_work_time[device_code] = tmp_work_time_list
        update_context("devices_work_time", devices_work_time)
        get_logger().info('get_calendar_work_time processed devices_work_time: {}'.format(devices_work_time))
    except Exception as e:
        get_logger().error("get_calendar_work_time error {}".format(e))


class RabbitMQConfig(RabbitMQConfigBasic):

    # def get_rabbitmq_host(self):
    #     return "**************"


    # def get_rabbitmq_port(self):
    #     return "5672"


    # def get_rabbitmq_username(self):
    #     return "rabbitmq"


    # def get_rabbitmq_password(self):
    #     return "realtech@123"
 
    def get_rabbitmq_subscribe_list(self):
        return [
            "general_kpi_list",
            "device_kpi_list",
            "device_special_formula",
            "device_mb_formula",
            "formula_ng_point_map",
            "WebLayout_produce_unit_import_application_used",
            production_calendar_start_time_config_key,
            "production_calendar_device_shift_config",
            "organization_user_info",
            jp_rapid_config_key,
            jp_value_realtime_key,
            RAPID_RESPONSE_OTHER_CONFIG_MQ,
            RAPID_RESPONSE_TIME_RANGE_LIST_MQ,
        ]

    def message_process_func(self):
        async def rabbitmq_message_func_examlple(message):
            get_logger().debug("message~~~~~~~~~~~~~~~~~~~~:%s", message)
            key = message["key"]
            data = message["data"]
            await rabbitmq_message_func(key, data)
            # get_device_code_list(message["data"])

        return rabbitmq_message_func_examlple

    def publish_config_data(self):
        return True


async def init_message():
    subscribe_message_cache = await rabbitmq_get_subscribe_message_cache()
    for message_key, message_data in subscribe_message_cache.items():
        if '~' in message_key:
            msg_org, msg_key = message_key.split('~')
            await rabbitmq_message_func(msg_key, message_data, msg_org)
        else:
            await rabbitmq_message_func(message_key, message_data)



class InitFuncConfig(InitFuncBasic):

    async def init_func(self):
        await idi_createTable(production_sequence_chart_data_collection, IDI_INDEX,sharding_colunms=["device_code"])
        await init_message()
        await init_quality_rapid_dict()  # 质量中断快反存储数据初始化到内存
        jp_value_timestamp_dict = await find_rapid_dict({'code': 'jp_value_timestamp_dict'})
        update_context("jp_value_timestamp_dict", jp_value_timestamp_dict)

