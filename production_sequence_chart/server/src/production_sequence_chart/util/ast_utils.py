
import ast
from mg_app_framework import get_context,get_logger,update_context

from production_sequence_chart.config import (cl_kpi)

def get_special_point_cl_lsit():
    try:
        cl_point_special_list = []
        cl_point_d2h_special_list = []
        device_special_formula_dict = get_context("device_special_formula_dict")
        for (device_code,compute_code),formula_dict in device_special_formula_dict.items():
            if compute_code == cl_kpi:
                cl_point_list,cl_point_d2h_list = get_kpi_in_formula(device_code,compute_code)
                cl_point_special_list.extend([(device_code,cl_point) for cl_point in cl_point_list])
                cl_point_d2h_special_list.extend([(device_code, cl_point) for cl_point in cl_point_d2h_list])
        update_context("cl_point_special_list",cl_point_special_list)
        update_context("cl_point_d2h_special_list",cl_point_d2h_special_list)
        get_logger().info("cl_point_special_list======================%s",cl_point_special_list)
        get_logger().info("cl_point_d2h_special_list======================%s", cl_point_d2h_special_list)
    except Exception as e:
        get_logger().error("get_point_cl_dict s",e)


def get_kpi_in_formula(device_code,compute_code):
    device_special_formula_dict = get_context("device_special_formula_dict")
    cl_kpi_in_formula_list = []
    cl_kpi_d2h_in_formula_list = []
    formula = device_special_formula_dict[(device_code, compute_code)]["formula_in_action"]
    formula_tree = ast.parse(formula)
    for node in ast.walk(formula_tree):
        if isinstance(node,ast.Call):
            if node.func.id == "sum1":
                cl_kpi_in_formula_list.extend([i.id for i in node.args])
            if node.func.id == "sum1_d2h":
                cl_kpi_d2h_in_formula_list.extend([i.id for i in node.args])
    return cl_kpi_in_formula_list,cl_kpi_d2h_in_formula_list
