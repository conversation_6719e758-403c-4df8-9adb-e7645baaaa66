from asyncio import Lock, iscoroutinefunction
from functools import wraps
from time import time

from mg_app_framework import (
    get_logger, update_context, get_context,
    get_handler, TaskKey, get_organization
)


def with_lock(process_func):
    @wraps(process_func)
    async def run(*args, **kwargs):
        lock_name = process_func.__name__
        if 'lock_id' in kwargs and kwargs.get('lock_id'):
            if kwargs.get('lock_id'):
                lock_name = kwargs.get('lock_id')
        mutex_context = get_context(lock_name)
        if mutex_context is None:
            mutex_context = Lock()
            update_context(lock_name, mutex_context)
        async with mutex_context:
            # get_logger().info('process lock for:{} in func:{}'.format(lock_name, process_func.__name__))
            await process_func(*args, **kwargs)

    return run


def time_costs(f):
    @wraps(f)
    def running_fun(*args, **kwargs):
        start_time = time()
        result = f(*args, **kwargs)
        end_time = time()
        get_logger().info('{} costs {}s'.format(f.__name__, end_time - start_time))
        return result

    @wraps(f)
    async def running_async_fun(*args, **kwargs):
        start_time = time()
        result = await f(*args, **kwargs)
        end_time = time()
        get_logger().info('{} costs {}s'.format(f.__name__, end_time - start_time))
        return result

    return running_async_fun if iscoroutinefunction(f) else running_fun


def raise_exception(f):
    @wraps(f)
    def running_fun(*args, **kwargs):
        try:
            result = f(*args, **kwargs)
        except Exception as e:
            get_logger().info('Error occured in {} cause {}'.format(f.__name__, str(e)))
        else:
            return result

    @wraps(f)
    async def running_async_fun(*args, **kwargs):
        try:
            result = await f(*args, **kwargs)
        except Exception as e:
            get_logger().info('Error occured in {} cause {}'.format(f.__name__, str(e)))
        else:
            return result

    return running_async_fun if iscoroutinefunction(f) else running_fun


@time_costs
async def deploy_message_via_mq(key, data, org=None, time_series_data=True):
    get_logger().debug("=" * 20)
    get_logger().debug('deploy')
    get_logger().debug(key)
    get_logger().debug(data)
    if org is None:
        org = get_organization()

    mq_handler = get_handler(TaskKey.rabbitmq_async)
    await mq_handler.publish_message({
        'key': key,
        'data': data
    }, org, time_series_data=time_series_data)


status_map = {
    2: "待机",  # "其他中断",  # 正常待机（也算做其他中断）【待机】
    3: "故障",  # "故障中断",  # 故障【故障】
    4: "断流",  # "缺料中断",  # 缺料（指的是流水线上下游间缺料）【断流】
    5: "缺料",  # "缺料中断",  # 缺料（指的是机器辅料缺料）【缺料】
    6: "满料",  # "其他中断",  # 满料【满料】
}
