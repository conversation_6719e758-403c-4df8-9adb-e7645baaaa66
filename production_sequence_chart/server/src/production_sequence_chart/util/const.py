WECHAT_MSG = {
    "56ad2cd9-67ae-4313-b2c8-08f4baec00b7": "100",  # 最大值
    "57621d10-162f-4a6f-b2f6-1ca239c56c1b": "110",  # 当前值
    "d433c21c-738d-40c3-8afa-9887405a6f8f": "90",  # 最小值
    "d7d45cbd-65fd-4466-bca1-7d222121f642": "设备名称",  # 设备名称
    "ee3d0241-95bb-44f6-90ef-1d684ac6550e": "额定节拍",  # 指标名称
    "task_end_time": "2020-03-06 18:00:00",  # 写空字符串或者当前时间
    "task_desc": "车间产线设备以及具体描述",  # 任务描述
    "create_time": "2020-03-06 14:19:36",  # 当前你的系统时间
    "timestamp": "2020-03-06 14:19:36",  # 保持当前系统时间一致
    "level": "instruction_warning_level0",  # 当前告警等级
    "level_desc": "通知",  # 和level对应
    "desc": "车间产线设备以及具体描述",  # 和task_desc保持一样
    "token": "5e61eb788077ab1b3ba63e99",  # 任务生命周期的标志
    "notify": {  # 处理人
        "instruction_warning_level0": [
            # {
            #   "code": "wangmeng_007",
            #   "identifier": "wangmeng_007",
            #   "name": "王萌"
            # }
        ]
    },
    "checker": {  # 检查人
        "instruction_warning_level0": []
    },
    "copy": {  # 抄送人
        "instruction_warning_level0": []
    },
    # 以上字段需要更新
    "checklist": {
        "默认检查分组": []
    },
    "creator": "admin",
    "customized": True,
    "instruction_code": "0078085f-7457-4c02-a5b1-aa89d8e7d3a3",
    "instruction_instance_code": "58170f98-7243-45d7-b003-2f87715b1a45",
    "instruction_instance_name": "计算指标快反",
    "instruction_name": "生产节拍监控",
    "instruction_warning_level0": "",
    "name": None,
    "rule_type": "当时间到达",
    "rule_value_type": "datetime",
    "user_defined": True,
    "device_code": None,
}

### 不良上报指标配置
ng_index_list = [
    {"code": "zb_r_hdts_rlj", "label": "换刀调试"},
    {"code": "zb_r_nfps_rlj", "label": "碰伤"},
    {"code": "zb_r_nfjbcc_rlj", "label": "基本径尺寸不良"},
    {"code": "zb_r_xkcc_rlj", "label": "销孔径尺寸不良"},
    {"code": "zb_r_nfhdccbl_rlj", "label": "换刀具尺寸不良"},
    {"code": "zb_r_xkhh_rlj", "label": "销孔划痕"},
    {"code": "zb_r_pz_rlj", "label": "换品种"},
    {"code": "zb_r_nfqt_rlj", "label": "其他"},
]