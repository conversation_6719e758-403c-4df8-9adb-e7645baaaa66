"""
时序图数据修改, 后续可以考虑使用模式拆分
"""
from asyncio import gather
from typing import List, Dict
from datetime import datetime, timedelta

from mg_app_framework import get_logger

from production_sequence_chart.config import p_cl_pre_kpi, IDI_COLLECTION_NAME, \
    production_sequence_chart_data_collection
from production_sequence_chart.util.common import with_lock
from production_sequence_chart.util.mongo_operator import find_one_mongodb, find_update_one_mongodb

SPAN_LIMIT = 0.5
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"


def trance_timestamps(timestamps) -> datetime:
    return datetime.strptime(timestamps, DATE_FORMAT)


@with_lock
async def upsert_device_production_sequence_jp_records(
        device_code: str,
        cl_pre_num: float,
        start_time: str,
        end_time: str,
        lock_id=None
):
    """
    获取设备的产量pre
    :param device_code:
    :param cl_pre_num:
    :param start_time:
    :param end_time:
    :param lock_id:
    :return:
    """
    start_time = trance_timestamps(start_time)
    end_time = trance_timestamps(end_time)
    # seq1: 前置插入校验
    if cl_pre_num <= 0:
        raise ValueError("设置产量异常,产量pre必须大于0!")

    if end_time <= start_time:
        raise ValueError("更新时间范围异常,结束时间:{},必须大于开始时间:{}!".format(end_time, start_time))

    is_cl_pre_exist = await find_one_mongodb(
        IDI_COLLECTION_NAME, {
            "code": "{}~{}".format(device_code, p_cl_pre_kpi),
            "timestamp": {
                "$gte": start_time, "$lte": end_time
            }}, {"_id": 0})
    if is_cl_pre_exist:
        raise ValueError("设置产量pre异常,当前时间区间内存在产量pre!")
    is_sequence_jp_exist = await find_one_mongodb(production_sequence_chart_data_collection, {
        "device_code": device_code,
        "timestamp": {
            "$gte": start_time.strftime(DATE_FORMAT),
            "$lte": end_time.strftime(DATE_FORMAT)
        }}, {"_id": 0})
    if is_sequence_jp_exist:
        raise ValueError("设置产量pre异常,当前时间区间内存在节拍时序图数据!")

    time_section_beat = round((end_time - start_time).total_seconds() / cl_pre_num, 2)  # 节拍 = 生产时间(单位:s) / 生产数量
    if time_section_beat <= SPAN_LIMIT:
        raise ValueError("节拍计算异常,当前计算节拍:{},需要保证计算大于节拍:{}".format(time_section_beat, SPAN_LIMIT))

    # seq2: 获取插入的节拍列表
    sequence_jp_records = make_device_production_sequence_jp_records(
        device_code, cl_pre_num, start_time, end_time
    )
    if not sequence_jp_records:
        raise ValueError("节拍计算异常,计算节拍时序图数据为空,无需处理!")
    cl_pre_record = {
        "code": "{}~{}".format(device_code, p_cl_pre_kpi),
        "value": cl_pre_num,
        "timestamp": end_time
    }
    # seq3: 保存到数据库, 1. 插入对应的产量pre值, 2. 插入对应的节拍数据
    await find_update_one_mongodb(
        IDI_COLLECTION_NAME, {
            "code": cl_pre_record["code"],
            "timestamp": cl_pre_record["timestamp"]
        }, {"$set": cl_pre_record}, upsert=True)
    jp_update_task = []
    for jp_record in sequence_jp_records:
        jp_update_task.append(
            find_update_one_mongodb(
                production_sequence_chart_data_collection, {
                    "device_code": jp_record["device_code"],
                    "timestamp": jp_record["timestamp"],
                }, {"$set": jp_record}, upsert=True)
        )
    await gather(*jp_update_task)
    get_logger().info("upsert_device_production_sequence_jp_records suc,device_code:{}, cl_pre_num:{},"
                      " start_time:{}, end_time:{}!".format(device_code, cl_pre_num, start_time, end_time))


def make_device_production_sequence_jp_records(
        device_code: str,
        cl_pre_num: float,
        start_time: datetime,
        end_time: datetime,
        info: str = "",
        sub_type="p_cl"
) -> List[Dict]:
    """
    根据指定时间区间的产量pre, 计算区间内的节拍值
    :param device_code:
    :param cl_pre_num: 产量pre
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param info:
    :param sub_type:
    :return:
    """
    time_section_seconds = (end_time - start_time).total_seconds()
    step_seconds = time_section_seconds / cl_pre_num  # 节拍
    if step_seconds <= SPAN_LIMIT:
        raise ValueError(
            "step_seconds is too litter,"
            "device_code:%s,start_time:%s,end_time:%s", device_code, end_time, start_time)
    jp_value = round(time_section_seconds / cl_pre_num, 2)
    count = 0
    last_timestamp_list = []
    last_timestamp_date = start_time
    while last_timestamp_date < end_time and count < cl_pre_num:
        count += 1
        last_timestamp_date += timedelta(seconds=step_seconds)
        last_timestamp_list.append(last_timestamp_date)
    result = []
    for timestamp_date in last_timestamp_list:
        result.append({
            "timestamp": timestamp_date.strftime(DATE_FORMAT),
            "device_code": device_code,
            "value": jp_value,
            "info": info or [],
            "sub_type": sub_type,
            "modify": True  # 添加一个标志位, 表明为用户插入数据
        })
    if count < cl_pre_num:
        get_logger().error("make_device_production_sequence_jp_records length error,"
                           "device_code:%s,cl_pre_num:%s,insert count:%s", device_code, cl_pre_num, count)

    return result


if __name__ == '__main__':
    from pprint import pprint

    pprint(make_device_production_sequence_jp_records("device_1_1", 131, datetime(2022, 5, 29), datetime(2022, 5, 30)))
