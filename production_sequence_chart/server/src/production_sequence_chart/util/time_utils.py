from mg_app_framework import get_logger, HttpClient, get_store,get_handler,Task<PERSON><PERSON>
from datetime import datetime, timedelta
from tornado.httpclient import AsyncHTTPClient
import urllib
import string
import calendar

date_format = "%Y-%m-%d %H:%M:%S"


def get_now():
    return datetime.now()

async def get_data(url, data):
    client = HttpClient(AsyncHTTPClient(max_clients=1000))
    url = urllib.parse.quote(url, safe=string.printable)
    res = await client.get(url, data=data, request_timeout=60)
    client.close()
    return res.data


async def post_kpi_data(data):
    client = HttpClient(AsyncHTTPClient(max_clients=1000))
    url = get_store().get_kpi_monitor_msg_post_url()
    url = urllib.parse.quote(url, safe=string.printable)
    res = await client.post(url, data=data)
    client.close()
    return res.data

def is_month_first_day(hour_datetime):
    day = hour_datetime.day
    return True if day == 1 else False

def get_month_first_day(hour_datetime):
    return datetime.strptime(hour_datetime.strftime("%Y-%m"), "%Y-%m")

def get_current_date(hour_datetime):
    return datetime.strptime(hour_datetime.strftime("%Y-%m-%d"), "%Y-%m-%d")


def get_current_hour(hour_datetime):
    if isinstance(hour_datetime, str):
        return datetime.strptime(hour_datetime[0:16], "%Y-%m-%d %H")
    else:
        return datetime.strptime(hour_datetime.strftime("%Y-%m-%d %H"), "%Y-%m-%d %H")


def get_current_minute(hour_datetime):
    if isinstance(hour_datetime, str):
        return datetime.strptime(hour_datetime[0:16], "%Y-%m-%d %H:%M")
    else:
        return datetime.strptime(hour_datetime.strftime("%Y-%m-%d %H:%M"), "%Y-%m-%d %H:%M")


def get_time_range(start_cron_datetime, end_cron_datetime, span_hours):
    logger = get_logger()
    if start_cron_datetime > end_cron_datetime:
        logger.error("start_cron_datetime bigger than end_cron_datetime")
        return []
    start_cron_datetime = start_cron_datetime - timedelta(hours=(start_cron_datetime.hour % span_hours))
    total_seconds = int((end_cron_datetime - start_cron_datetime).total_seconds())
    if total_seconds == 0:
        return [start_cron_datetime]
    span_seconds = span_hours * 3600
    time_list = []
    for i in range(0, total_seconds, span_seconds):
        time_list.append(start_cron_datetime + timedelta(seconds=span_seconds))
    return time_list


def get_shift_by_timestamp(collect_timestamp):
    shift_index = collect_timestamp.hour
    if 0 <= shift_index < 8:
        shift_name = "早班"
    elif 8 <= shift_index < 16:
        shift_name = "中班"
    else:
        shift_name = "晚班"
    return {"shift_index": shift_index, "shift_name": shift_name}


def get_time_dict(shift_start_time):
    shift_name = get_shift_by_timestamp(shift_start_time)["shift_name"]
    shift_code = shift_start_time.hour
    return {
        "collect_timestamp": shift_start_time.strftime("%Y-%m-%d %H:%M:%S"),
        "shift_name": shift_name,
        "shift_index": shift_code,
    }


async def get_total_kpi_msg_dict(lab_msg, tag_msg, compute_msg):
    global total_kpi_msg_dict
    total_kpi_msg = []
    for i in compute_msg:
        i["kpi_type"] = "kpi_compute"
        total_kpi_msg.append(i)
    for i in lab_msg:
        i["kpi_type"] = "kpi_lab"
        total_kpi_msg.append(i)
    for i in tag_msg:
        i["kpi_type"] = "kpi_tag"
        total_kpi_msg.append(i)
    total_kpi_msg_dict = {i["code"]: i
                          for i in total_kpi_msg if type(i) == dict}
    return total_kpi_msg_dict


def get_tag_or_lab_msg_agg(lab_msg, tag_msg):
    total = []
    for i in lab_msg:
        i["kpi_type"] = "kpi_lab"
        total.append(i)
    for i in tag_msg:
        i["kpi_type"] = "kpi_tag"
        total.append(i)
    return total


def get_timedelta_by_statistical_period(task_start_time, cron_unit):
    if isinstance(task_start_time, str):
        task_start_time = datetime.strptime(task_start_time, date_format)
    hours = 0
    if cron_unit == '小时':
        hours = 1
    elif cron_unit == '两小时':
        hours = 2
    elif cron_unit == '四小时':
        hours = 4
    elif cron_unit == '八小时':
        hours = 8
    elif cron_unit == '半天':
        hours = 12
    elif cron_unit == '天':
        hours = 24
    elif cron_unit == '周':
        hours = 24 * 7
    elif cron_unit == '月':
        days = calendar.monthrange(task_start_time.year, task_start_time.month)[1]
        hours = 24 * days
    elif cron_unit == '年':
        year = task_start_time.year
        if (year % 4 == 0 and year % 100 != 0) or year % 400 == 0:
            days = 366
        else:
            days = 365
        hours = 24 * days
    elif cron_unit == '分钟':
        hours = 1 / 60
    elif cron_unit == '白班':
        hours = 8
    return hours


def check_if_equal(v1, v2):
    ratio = float(v1) / float(v2)
    if ratio >= 0.9 and ratio <= 1.1:
        return True
    else:
        return False

def get_work_time_range(data):
    tmp_data_list = [[k, v] for k, v in sorted(data.items(), key=lambda x: x[0])]
    length = len(tmp_data_list)
    max_value = tmp_data_list[length - 1][1]
    if max_value != 0:
        # last_off_time = get_now().strftime(date_format)
        last_off_time = tmp_data_list[length-1][0]
        last_off_date = datetime.strptime(last_off_time,date_format)
        if last_off_date.strftime("%Y-%m-%d") == datetime.now().strftime("%Y-%m-%d"):
            last_off_time = datetime.now().strftime(date_format)
        else:
            if last_off_date.strftime("%H:%M:%S") == "00:00:00":
                pass
            else:
                last_off_time = (last_off_date + timedelta(days=1)).strftime("%Y-%m-%d 00:00:00")
    else:
        for i in range(length - 1, 0, -1):
            if tmp_data_list[i][1]:
                last_off_time = tmp_data_list[i + 1][0]
                break
    for i in range(len(tmp_data_list)):
        if tmp_data_list[i][1]:
            first_on_time = tmp_data_list[i][0]
            break
    return first_on_time,last_off_time


def get_start_time(task_start_time, compute_index_dict):
    statistical_period = compute_index_dict.get("statistical_period")
    cal_time = compute_index_dict.get("cal_time", None)
    if_current = True if cal_time and  cal_time != statistical_period else False
    if not cal_time:
        cal_time = statistical_period
    cal_hour = get_timedelta_by_statistical_period(task_start_time,cal_time)
    start_hour = task_start_time.hour
    hour_statistical_list = ["小时", "两小时", "四小时", "八小时", "半天"]
    if isinstance(task_start_time, str):
        task_start_time = datetime.strptime(task_start_time, date_format)
    if cal_time in hour_statistical_list:
        shift_start_time = get_current_hour(task_start_time - timedelta(hours=start_hour % cal_hour))
    elif cal_time == "分钟":
        shift_start_time = get_current_minute(task_start_time)
    elif cal_time == '天':
        shift_start_time = get_current_date(task_start_time)
    elif cal_time == "周":
        weekday = task_start_time.weekday()
        shift_start_time = get_current_hour(task_start_time - timedelta(days=weekday, hours=start_hour))
    elif cal_time == "月":
        shift_start_time = datetime(task_start_time.year, task_start_time.month, 1)
    elif cal_time == "年":
        shift_start_time = datetime(task_start_time.year, 1, 1)
    else:
        raise Exception("统计周期错误")
    if if_current:
        if cal_time == "月" and is_month_first_day(task_start_time):
            last_month_end_dateime = task_start_time - timedelta(days=1)
            shift_start_time = datetime(last_month_end_dateime.year, last_month_end_dateime.month, 1)
        if statistical_period in ["天","周","月","年"]:
            return shift_start_time,get_current_date(task_start_time),if_current
        else:
            return shift_start_time, task_start_time, if_current
    else:
        last_shift_time = shift_start_time - timedelta(hours=cal_hour)
        return last_shift_time, shift_start_time,if_current


async def publish_msg(key,msg):
    try:
        get_logger().info("publish_msg~~~~~~~~~~~~~~~~~~~:%s,key:%s",msg,key)
        rabbitmq_handler = get_handler(TaskKey.rabbitmq_async)
        await rabbitmq_handler.publish_message({
            'key': key,
            'data': msg,
        })
    except Exception as e:
        get_logger().error("publish_msg key:%s,msg:%s, error:%s",key,msg,e)
