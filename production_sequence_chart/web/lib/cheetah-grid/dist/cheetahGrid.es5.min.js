/*! Cheetah Grid v0.13.8 | license MIT */
!function(){"use strict";function t(e,n,o,a){return(t="undefined"!=typeof Reflect&&Reflect.set?Reflect.set:function(t,e,n,o){var a,l=r(t,e);if(l){if((a=Object.getOwnPropertyDescriptor(l,e)).set)return a.set.call(o,n),!0;if(!a.writable)return!1}if(a=Object.getOwnPropertyDescriptor(o,e)){if(!a.writable)return!1;a.value=n,Object.defineProperty(o,e,a)}else i(o,e,n);return!0})(e,n,o,a)}function e(e,n,r,o,i){if(!t(e,n,r,o||e)&&i)throw new Error("failed to set property");return r}function n(t,e,o){return(n="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var o=r(t,e);if(o){var i=Object.getOwnPropertyDescriptor(o,e);return i.get?i.get.call(n):i.value}})(t,e,o||t)}function r(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function a(t,e){return!e||"object"!==g(e)&&"function"!=typeof e?l(t):e}function l(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&u(t,e)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function f(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function h(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function d(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function v(t,e,n){return e&&d(t.prototype,e),n&&d(t,n),t}function g(t){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}(function(t){var r,u;r=t,u=function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===g(t)&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=67)}([function(e,n,r){(function(n){var r,o,i=void 0===t,a={get find(){return r||(r=Array.prototype.find?function(t,e){return Array.prototype.find.call(t,e)}:function(t,e){var n=a.findIndex(t,e);return n>=0?t[n]:void 0})},get findIndex(){return o||(o=Array.prototype.findIndex?function(t,e){return Array.prototype.findIndex.call(t,e)}:function(t,e){for(var n=t.length,r=0;r<n;r++)if(e(t[r],r,t))return r;return-1})}};function l(t){return null!=t}var s=function(){if(i)return{IE:!1,Edge:!1,Chrome:!1,Firefox:!1,Safari:!1};var e=t.navigator.userAgent.toLowerCase();return{IE:e.match(/(msie)/)||e.match(/trident/),Edge:e.indexOf("edge")>-1,Chrome:e.indexOf("chrome")>-1&&-1===e.indexOf("edge"),Firefox:e.indexOf("firefox")>-1,Safari:e.indexOf("safari")>-1&&-1===e.indexOf("edge")}}(),c=s.IE,u=s.Chrome,f=s.Firefox,h=s.Edge,d=s.Safari;function v(t,e){for(var n=t,r=0;r<(arguments.length<=2?0:arguments.length-2)&&l(n);r++)n=e(n,r+2<2||arguments.length<=r+2?void 0:arguments[r+2]);return n}var g=function(t){return t&&"function"==typeof t.then};e.exports={isNode:i,isDef:l,browser:{IE:c,Edge:h,Chrome:u,Firefox:f,Safari:d,heightLimit:u?33554431:f?17895588:10737433},extend:function(){for(var t={},e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return n.forEach((function(e){var n=function(n){Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){e[n]=t},configurable:!0,enumerable:!0})};for(var r in e)n(r)})),t},isDescendantElement:function(t,e){for(;e.parentElement;){var n=e.parentElement;if(t===n)return!0;e=n}return!1},getChainSafe:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return v.apply(void 0,[t,function(t,e){return t[e]}].concat(n))},applyChainSafe:v,getOrApply:function(t){if("function"==typeof t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return t.apply(void 0,n)}return t},getIgnoreCase:function(t,e){if(t[e])return t[e];var n=e.toLowerCase();if(t[n])return t[n];var r=e.toLowerCase();if(t[r])return t[r];for(var o in t)if(o.toLowerCase()===n)return t[o]},isPromise:g,then:function(t,e){return g(t)?t.then((function(t){return e(t)})):e(t)},array:a,obj:{setReadonly:function(t,e,n){Object.defineProperty(t,e,{enumerable:!1,configurable:!0,value:n})},each:function(t,e){for(var n in t)e(t[n],n,t)},isObject:function(t){return t===Object(t)}},str:{endsWith:function(t,e,n){var r=t.toString();("number"!=typeof n||!isFinite(n)||Math.floor(n)!==n||n>r.length)&&(n=r.length),n-=e.length;var o=r.lastIndexOf(e,n);return-1!==o&&o===n},genChars:function(t){var e=/([\uD800-\uDBFF][\uDC00-\uDFFF]|\r\n|[^\uD800-\uDFFF])([\u180B-\u180D]|[\uFE00-\uFE0F]|\uDB40[\uDD00-\uDDEF])?/g;return{next:function(){var n=e.exec(t);return null!==n?n[0]:null}}},genWords:function(t){var e=/[!-~]+|[^!-~\s]+|\s+/g;return{next:function(){var n=e.exec(t);return null!==n?n[0]:null}}}},event:{getMouseButtons:function(t){return l(t.buttons)?t.buttons:l(t.which)?3===t.which?4:2===t.which?4:t.which:0===t.button||1===t.button?1:2===t.button?2:0},getKeyCode:function(t){return t.keyCode||t.which},isTouchEvent:function(t){return!!t.changedTouches},cancel:function(t){t.preventDefault(),t.stopPropagation()}},style:{toBoxArray:function(t){return n(t)?3===t.length?[t[0],t[1],t[2],t[1]]:2===t.length?[t[0],t[1],t[0],t[1]]:1===t.length?[t[0],t[0],t[0],t[0]]:t:[t,t,t,t]}},emptyFn:function(){}}}).call(this,r(2).Array_isArray)},function(e,n,r){(function(n){var o=r(0).isNode?n.Symbol:t.Symbol?t.Symbol:function(){function t(){for(var t="abcdefghijklmnopqrstuvwxyz0123456789",e=t.length,n="",r=0;r<10;r++)n+=t[Math.floor(Math.random()*e)];return n}return function(e){return e?"#".concat(e,"_").concat(t()):"#_".concat(t())}}(),i={};e.exports={get:function(t){return t?i[t]?i[t]:i[t]=o(t):o()},get PROTECTED_SYMBOL(){return this.get("protected")},get CHECK_COLUMN_STATE_ID(){return this.get("chkcol.stateID")},get BUTTON_COLUMN_STATE_ID(){return this.get("btncol.stateID")},get COLUMN_FADEIN_STATE_ID(){return this.get("col.fadein_stateID")},get BRANCH_GRAPH_COLUMN_STATE_ID(){return this.get("branch_graph_col.stateID")},get SMALL_DIALOG_INPUT_EDITOR_STATE_ID(){return this.get("small_dialog_input_editor.stateID")},get INLINE_INPUT_EDITOR_STATE_ID(){return this.get("inline_input_editor.stateID")},get INLINE_MENU_EDITOR_STATE_ID(){return this.get("inline_menu_editor.stateID")},get CHECK_HEADER_STATE_ID(){return this.get("check_header.stateID")}}}).call(this,r(69))},function(e,n,r){(function(n){e.exports={Array_isArray:void 0===t?n:t.Array.isArray}}).call(this,r(2).Array_isArray)},function(t,e,n){var r=n(0).obj.each,o=1,i=function(){function t(){h(this,t),this._listeners={}}return v(t,[{key:"on",value:function(t,e,n){for(var r=arguments.length,i=new Array(r>3?r-3:0),a=3;a<r;a++)i[a-3]=arguments[a];t.addEventListener&&t.addEventListener.apply(t,[e,n].concat(i));var l={target:t,type:e,listener:n,options:i},s=o++;return this._listeners[s]=l,s}},{key:"once",value:function(t,e,n){for(var r=this,o=arguments.length,i=new Array(o>3?o-3:0),a=3;a<o;a++)i[a-3]=arguments[a];var l=this.on.apply(this,[t,e,function(){n.apply(void 0,arguments),r.off(l)}].concat(i));return l}},{key:"tryWithOffEvents",value:function(t,e,n){var o=[];try{r(this._listeners,(function(n){var r;n.target===t&&n.type===e&&(n.target.removeEventListener&&(r=n.target).removeEventListener.apply(r,[n.type,n.listener].concat(f(n.options))),o.push(n))})),n()}finally{o.forEach((function(t){var e;t.target.addEventListener&&(e=t.target).addEventListener.apply(e,[t.type,t.listener].concat(f(t.options)))}))}}},{key:"off",value:function(t){if(t){var e,n=this._listeners[t];n&&(delete this._listeners[t],n.target.removeEventListener&&(e=n.target).removeEventListener.apply(e,[n.type,n.listener].concat(f(n.options))))}}},{key:"fire",value:function(t,e){for(var n=arguments.length,o=new Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i];r(this._listeners,(function(n){var r;n.target===t&&n.type===e&&(r=n.listener).call.apply(r,[n.target].concat(o))}))}},{key:"hasListener",value:function(t,e){var n=!1;return r(this._listeners,(function(r){r.target===t&&r.type===e&&(n=!0)})),n}},{key:"clear",value:function(){r(this._listeners,(function(t){var e;t.target.removeEventListener&&(e=t.target).removeEventListener.apply(e,[t.type,t.listener].concat(f(t.options)))})),this._listeners={}}},{key:"dispose",value:function(){this.clear(),this._listeners=null}}]),t}();t.exports=i},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._color=n.color,t._font=n.font,t._padding=n.padding,t._textOverflow=n.textOverflow||"clip",t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"color",get:function(){return this._color},set:function(t){this._color=t,this.doChangeStyle()}},{key:"font",get:function(){return this._font},set:function(t){this._font=t,this.doChangeStyle()}},{key:"padding",get:function(){return this._padding},set:function(t){this._padding=t,this.doChangeStyle()}},{key:"textOverflow",get:function(){return this._textOverflow},set:function(t){this._textOverflow=t,this.doChangeStyle()}}]),e}(n(23));t.exports=o},function(e,r,u){var f=u(0),d=f.isDef,g=f.browser,p=f.isDescendantElement,y=f.array,_=f.isPromise,m=f.event,w=m.isTouchEvent,C=m.getMouseButtons,x=m.getKeyCode,b=m.cancel,k=u(14),E=u(19),I=u(70),L=u(3),S=u(74),T=u(35),A=u(20),R=u(8).getFontSize,O=u(75),B=u(1).PROTECTED_SYMBOL,M=35,D=36,P=37,F=38,z=39,W=40,H=67,N=86,U="context_menu",V="click_cell",j="dblclick_cell",G="dbltap_cell",K="mousedown_cell",Y="mouseup_cell",q="selected_cell",X="keydown",$="mousemove_cell",Q="mouseenter_cell",Z="mouseleave_cell",J="mouseover_cell",tt="mouseout_cell",et="input_cell",nt="paste_cell",rt="scroll",ot="focus_grid",it="blur_grid",at={CONTEXT_MENU:U,CLICK_CELL:V,DBLCLICK_CELL:j,DBLTAP_CELL:G,MOUSEDOWN_CELL:K,MOUSEUP_CELL:Y,SELECTED_CELL:q,KEYDOWN:X,MOUSEMOVE_CELL:$,MOUSEENTER_CELL:Q,MOUSELEAVE_CELL:Z,MOUSEOVER_CELL:J,MOUSEOUT_CELL:tt,INPUT_CELL:et,PASTE_CELL:nt,EDITABLEINPUT_CELL:"editableinput_cell",MODIFY_STATUS_EDITABLEINPUT_CELL:"modify_status_editableinput_cell",RESIZE_COLUMN:"resize_column",SCROLL:rt,FOCUS_GRID:ot,BLUR_GRID:it};function lt(t){navigator.vibrate&&w(t)&&navigator.vibrate(50)}function st(t,e){var n=t.getTargetRowAtInternal(e);if(d(n))return n;var r=Math.min(Math.ceil(e/t[B].defaultRowHeight),t.rowCount-1),o=Tt(t,0,r);return e>=o?function(n,r){for(var o=r-St(t,n),i=t[B].rowCount,a=n;a<i;a++){var l=o+St(t,a);if(o<=e&&e<l)return{top:o,row:a};o=l}return null}(r,o):function(n,r){for(var o=r,i=n;i>=0;i--){var a=o-St(t,i);if(a<=e&&e<o)return{top:a,row:i};o=a}return null}(r,o)}function ct(t,e){for(var n=0,r=t[B].colCount,o=0;o<r;o++){var i=n+Et(t,o);if(i>e)return{left:n,col:o};n=i}return null}function ut(t,e){if(!t[B].frozenRowCount)return null;for(var n=t[B].scroll.top,r=t[B].frozenRowCount,o=0;o<r;o++){var i=n+St(t,o);if(i>e)return{top:n,row:o};n=i}return null}function ft(t,e){if(!t[B].frozenColCount)return null;for(var n=t[B].scroll.left,r=t[B].frozenColCount,o=0;o<r;o++){var i=n+Et(t,o);if(i>e)return{left:n,col:o};n=i}return null}function ht(t){if(!t[B].frozenRowCount)return null;for(var e=t[B].scroll.top,n=0,r=t[B].frozenRowCount,o=0;o<r;o++)n+=St(t,o);return new E(t[B].scroll.left,e,t[B].canvas.width,n)}function dt(t){if(!t[B].frozenColCount)return null;for(var e=t[B].scroll.left,n=0,r=t[B].frozenColCount,o=0;o<r;o++)n+=Et(t,o);return new E(e,t[B].scroll.top,n,t[B].canvas.height)}function vt(t,e,n,r,o,i,a,l,s,c,u,f){var h=new E(r-s.left,a-s.top,o,l),d=E.bounds(Math.max(r,u)-s.left,Math.max(a,c)-s.top,h.right,h.bottom);if(d.height>0&&d.width>0){e.save();try{var v=function(t,e,n){return t[B].drawCells[n]?t[B].drawCells[n][e]:null}(t,n,i);v&&v.cancel();var g=new jt(n,i,e,h,d,!!v,t[B].selection,f),p=t.onDrawCell(n,i,g);if(_(p)){!function(t,e,n,r){t[B].drawCells[n]||(t[B].drawCells[n]={}),t[B].drawCells[n][e]=r}(t,n,i,g);var y=n;g._delayMode(t,(function(){!function(t,e,n){t[B].drawCells[n]&&(delete t[B].drawCells[n][e],0===Object.keys(t[B].drawCells[n]).length&&delete t[B].drawCells[n])}(t,y,i)})),p.then((function(){g.terminate()}))}}finally{e.restore()}}}function gt(t,e,n,r,o,i,a,l,s,c,u){var f=t[B].colCount,h=function(n,r){if(n>=f-1&&t[B].canvas.width>r-s.left){var o=r-s.left;e.save(),e.beginPath(),e.fillStyle=t.underlayBackgroundColor||"#F6F6F6",e.rect(o,a-s.top,t[B].canvas.width-o,l),e.fill(),e.restore()}},d=0;if(n){for(var v=n.left,g=t[B].frozenColCount,p=n.col;p<g;p++){var y=Et(t,p);if(vt(t,e,p,v,y,i,a,l,s,c,0,u),o<=(v+=y))return void h(p,v)}d=v}for(var _=r.left,m=r.col;m<f;m++){var w=Et(t,m);if(vt(t,e,m,_,w,i,a,l,s,c,d,u),o<=(_+=w))return void h(m,_)}h(f-1,_)}function pt(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!t.supportOpacity||n){var r=Mt(t),o=t[B].rowCount,i=t._getInitContext(),a=st(t,Math.max(r.top,e.top))||{top:Tt(t,0,o-1),row:o},l=ct(t,Math.max(r.left,e.left))||{left:Lt(t,0,t[B].colCount-1),col:t[B].colCount},s=Math.min(r.bottom,e.bottom),c=Math.min(r.right,e.right),u=ut(t,Math.max(r.top,e.top)),f=ft(t,Math.max(r.left,e.left)),h=new Ut,d=function(e,n){if(e>=o-1&&t[B].canvas.height>n-r.top){var a=n-r.top;i.save(),i.beginPath(),i.fillStyle=t.underlayBackgroundColor||"#F6F6F6",i.rect(0,a,t[B].canvas.width,t[B].canvas.height-a),i.fill(),i.restore()}},v=0;if(u){for(var g=u.top,p=t[B].frozenRowCount,y=u.row;y<p;y++){var _=St(t,y);if(gt(t,i,f,l,c,y,g,_,r,0,h),s<=(g+=_))return d(y,g),void h.draw(i)}v=g}for(var m=a.top,w=a.row;w<o;w++){var C=St(t,w);if(gt(t,i,f,l,c,w,m,C,r,v,h),s<=(m+=C))return d(w,m),void h.draw(i)}d(o-1,m),h.draw(i)}else{var x=t[B].canvas;x.width=x.width;var b=Mt(t);pt(t,b,!0)}}function yt(t,e){return Math.round(A.toPx(e,t[B].calcWidthContext))}function _t(t,e,n){var r=Ct(t,e);return Math.max(mt(r,n),0)}function mt(t,e){return t?t.min&&t.min>e?t.min:t.max&&t.max<e?t.max:e:e}function wt(t,e){var n=t[B].colWidthsMap.get(e);return n||t.defaultColWidth}function Ct(t,e){var n=t[B].colWidthsLimit[e];if(!n)return null;var r={};return n.min&&(r.min=yt(t,n.min),r.minDef=n.min),n.max&&(r.max=yt(t,n.max),r.maxDef=n.max),r}function xt(t){return t&&"string"==typeof t&&"auto"===t.toLowerCase()}function bt(t){for(var e=[],n=0,r=[],o=0;o<t[B].colCount;o++){var i=wt(t,o),a=Ct(t,o);if(xt(i))a&&r.push(a),n++;else{var l="number"==typeof i?"".concat(i,"px"):i;if(a){var s=yt(t,l),c=mt(a,s);s!==c&&(l="".concat(c,"px"))}e.push(l)}}if(r.length&&e.length){for(var u=yt(t,"calc(100% - (".concat(e.join(" + "),"))"))/n,f=0;f<r.length;f++){var h=r[f];h.min&&u<h.min?(e.push("number"==typeof h.minDef?"".concat(h.minDef,"px"):h.minDef),n--):h.max&&h.max<u&&(e.push("number"==typeof h.maxDef?"".concat(h.maxDef,"px"):h.maxDef),n--)}if(n<=0)return"".concat(u,"px")}return e.length?"calc((100% - (".concat(e.join(" + "),")) / ").concat(n,")"):"".concat(100/n,"%")}function kt(t,e){return xt(e)?yt(t,bt(t)):yt(t,e)}function Et(t,e){return _t(t,e,kt(t,wt(t,e)))}function It(t,e,n){t[B].colWidthsMap.put(e,n)}function Lt(t,e,n){var r=kt(t,t.defaultColWidth),o=r*(n-e+1);t[B].colWidthsMap.each(e,n,(function(e,n){o+=_t(t,n,kt(t,e))-r}));for(var i=e;i<=n;i++)if(!t[B].colWidthsMap.has(i)){var a=_t(t,i,r);a!==r&&(o+=a-r)}return o}function St(t,e){var n=t.getRowHeightInternal(e);if(d(n))return n;var r=t[B].rowHeightsMap.get(e);return r||t[B].defaultRowHeight}function Tt(t,e,n){var r=t.getRowsHeightInternal(e,n);if(d(r))return r;var o=n-e+1,i=t[B].defaultRowHeight*o;return t[B].rowHeightsMap.each(e,n,(function(e){i+=e-t[B].defaultRowHeight})),i}function At(t,e,n,r){var o=t.getOffsetInvalidateCells();function i(t){return o>0&&(t.start.col-=o,t.start.row-=o,t.end.col+=o,t.end.row+=o),t}var a=i(t.selection.range),l=t.getCellsRect(a.start.col,a.start.row,a.end.col,a.end.row);t.selection._setFocusCell(e,n,r),t.makeVisibleCell(e,n),t.focusCell(e,n);var s=i(t.selection.range),c=t.getCellsRect(s.start.col,s.start.row,s.end.col,s.end.row);c.intersection(l)?pt(t,E.max(c,l)):(pt(t,l),pt(t,c))}function Rt(e,n){w(n)&&(n=n.changedTouches[0]);var r=n.clientX||n.pageX+t.scrollX,o=n.clientY||n.pageY+t.scrollY,i=e[B].canvas.getBoundingClientRect();return i.right<=r?null:i.bottom<=o?null:{x:r-i.left+e[B].scroll.left,y:o-i.top+e[B].scroll.top}}function Ot(e){var n=e[B],r=n.handler,a=n.element,l=n.scrollable,s=function(t){var n=Rt(e,t);if(!n)return{};var r=e.getCellAt(n.x,n.y);return r.col<0||r.row<0?{abstractPos:n,cell:r}:{abstractPos:n,cell:r,eventArgs:{col:r.col,row:r.row,event:t}}},c=function(t){var n=e[B].colWidthsLimit[t];return!(n&&n.min&&n.max)||n.max!==n.min};r.on(a,"mousedown",(function(t){var n=s(t),r=n.abstractPos,o=n.eventArgs;if(r){if(o){var i=e.fireListeners(K,o);if(y.findIndex(i,(function(t){return!t}))>=0)return}if(1===C(t)){var a=Bt(e,r.x,r.y);a>=0&&c(a)?e[B].columnResizer.start(a,t):e[B].cellSelector.start(t)}}})),r.on(a,"mouseup",(function(t){if(e.hasListeners(Y)){var n=s(t).eventArgs;n&&e.fireListeners(Y,n)}}));var u=null,f=null;function h(t){f&&(clearTimeout(f),f=null)}r.on(a,"touchstart",(function(t){if(u){var n=s(t).eventArgs;if(n&&n.col===u.col&&n.row===u.row&&e.fireListeners(G,n),u=null,t.defaultPrevented)return}else u=s(t).eventArgs,setTimeout((function(){u=null}),350);f=setTimeout((function(){f=null;var n=Rt(e,t);if(n){var r=Bt(e,n.x,n.y,15);r>=0&&c(r)?e[B].columnResizer.start(r,t):e[B].cellSelector.start(t)}}),500)})),r.on(a,"touchcancel",h),r.on(a,"touchmove",h),r.on(a,"touchend",(function(t){f&&(clearTimeout(f),e[B].cellSelector.select(t),f=null)}));var v=!1,g=null,p=null;function _(t,n){e.fireListeners(Q,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach((function(e){i(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},t,{event:n})),g=t}function m(t){var n=g;g=null,n&&e.fireListeners(Z,{col:n.col,row:n.row,event:t})}function w(t){e.fireListeners(J,t),p=t}function k(){var t=p;p=null,t&&e.fireListeners(tt,{col:t.col,row:t.row})}var E=l.getElement();r.on(E,"mouseover",(function(t){v=!0})),r.on(E,"mouseout",(function(t){v=!1,k()})),r.on(a,"mouseleave",(function(t){m(t)})),r.on(a,"mousemove",(function(t){var n=s(t),r=n.abstractPos,o=n.eventArgs;if(o){var i=g;if(i)if(e.fireListeners($,o),i.col!==o.col||i.row!==o.row){k(),m(t);var l={col:o.col,row:o.row};_(l,t),v&&w(l)}else v&&!p&&w({col:o.col,row:o.row});else{var u={col:o.col,row:o.row};_(u,t),v&&w(u),e.fireListeners($,o)}}else k(),m(t);if(!e[B].columnResizer.moving(t)&&!e[B].cellSelector.moving(t)){var f=a.style;if(r){var h=Bt(e,r.x,r.y);h>=0&&c(h)?f.cursor="col-resize":"col-resize"===f.cursor&&(f.cursor="")}else"col-resize"===f.cursor&&(f.cursor="")}})),r.on(a,"click",(function(t){if(!e[B].columnResizer.lastMoving(t)&&!e[B].cellSelector.lastMoving(t)&&e.hasListeners(V)){var n=s(t).eventArgs;n&&e.fireListeners(V,n)}})),r.on(a,"dblclick",(function(t){if(e.hasListeners(j)){var n=s(t).eventArgs;n&&e.fireListeners(j,n)}})),r.on(a,"contextmenu",(function(t){if(e.hasListeners(U)){var n=s(t).eventArgs;n&&e.fireListeners(U,n)}}),{passive:!1}),e[B].focusControl.onKeyDown((function(t,n){e.fireListeners(X,t,n)})),e[B].selection.listen(q,(function(t){e.fireListeners(q,t,t.selected)})),l.onScroll((function(t){!function(t,e){var n=t[B].scroll.left,r=t[B].scroll.top,o=t[B].scrollable.scrollLeft-n,i=t[B].scrollable.scrollTop-r;t[B].scroll={left:t[B].scrollable.scrollLeft,top:t[B].scrollable.scrollTop};var a=Mt(t);if(Math.abs(o)>=a.width||Math.abs(i)>=a.height||t.supportOpacity)pt(t,a);else{if(t[B].context.drawImage(t[B].canvas,-o,-i),0!==o){var l=a.copy();if(o<0){if(l.width=-o,t[B].frozenColCount>0){var s=dt(t);l.width+=s.width}}else o>0&&(l.left=l.right-o);pt(t,l),o>0&&t[B].frozenColCount>0&&pt(t,dt(t))}if(0!==i){var c=a.copy();if(i<0){if(c.height=-i,t[B].frozenRowCount>0){var u=ht(t);c.height+=u.height}}else i>0&&(c.top=c.bottom-i);pt(t,c),i>0&&t[B].frozenRowCount>0&&pt(t,ht(t))}}}(e),e.fireListeners(rt,{event:t})})),e[B].focusControl.onKeyDownMove((function(t){!function(t,e){var n=e.shiftKey,r=x(e),o=n?t.selection._focus:t.selection._sel;if(r===P){var i=t.getMoveLeftColByKeyDownInternal(o);if(i<0)return;At(t,i,o.row,n),b(e)}else if(r===F){var a=t.getMoveUpRowByKeyDownInternal(o);if(a<0)return;At(t,o.col,a,n),b(e)}else if(r===z){var l=t.getMoveRightColByKeyDownInternal(o);if(t.colCount<=l)return;At(t,l,o.row,n),b(e)}else if(r===W){var s=t.getMoveDownRowByKeyDownInternal(o);if(t.rowCount<=s)return;At(t,o.col,s,n),b(e)}else if(r===D)At(t,0,e.ctrlKey?0:o.row,e.shiftKey),b(e);else if(r===M){var c=e.ctrlKey?t.rowCount-1:o.row;At(t,t.colCount-1,c,n),b(e)}}(e,t)})),e.listen("copydata",(function(n){for(var r="",o=n.start.row;o<=n.end.row;o++){for(var i=n.start.col;i<=n.end.col;i++){var a=e.getCopyCellValue(i,o);if(t.Promise&&a instanceof t.Promise);else{var l="".concat(a);l.match(/^\[object .*\]$/)||(r+=l)}i<n.end.col&&(r+="\t")}r+="\n"}return r})),e[B].focusControl.onCopy((function(t){return y.find(e.fireListeners("copydata",e[B].selection.range),d)})),e[B].focusControl.onPaste((function(t){var n=t.value,r=t.event,o=n.replace(/\r?\n$/,""),i=e[B].selection.select,a=i.col,l=i.row;e.fireListeners(nt,{col:a,row:l,value:n,normalizeValue:o,multi:/[\r\n\u2028\u2029\t]/.test(o),event:r})})),e[B].focusControl.onInput((function(t){var n=e[B].selection.select,r=n.col,o=n.row;e.fireListeners(et,{col:r,row:o,value:t})})),e[B].focusControl.onFocus((function(t){e.fireListeners(ot,t),e[B].focusedGrid=!0;var n=e[B].selection.select,r=n.col,o=n.row;e.invalidateCell(r,o)})),e[B].focusControl.onBlur((function(t){e.fireListeners(it,t),e[B].focusedGrid=!1;var n=e[B].selection.select,r=n.col,o=n.row;e.invalidateCell(r,o)})),e.bindEventsInternal()}function Bt(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:5;if(t[B].frozenRowCount<=0)return-1;if(!ht(t).inPoint(e,n))return-1;var o=t.getCellAt(e,n),i=t.getCellRect(o.col,o.row);return e<i.left+r?o.col-1:i.right-r<e?o.col:-1}function Mt(t){var e=t[B],n=e.scroll,r=n.left,o=n.top,i=e.canvas,a=i.width,l=i.height;return new E(r,o,a,l)}function Dt(t,e){var n=e.copy(),r=Mt(t);return n.offsetLeft(-r.left),n.offsetTop(-r.top),n}var Pt=function(){function t(e){h(this,t),this._grid=e,this._handler=new L,this._events={},this._started=!1,this._moved=!1}return v(t,[{key:"moving",value:function(t){return!!this._started}},{key:"lastMoving",value:function(t){if(this.moving(t))return!0;var e=this._mouseEndPoint;if(!e)return!1;var n=Rt(this._grid,t);return n.x===e.x&&n.y===e.y}},{key:"_bindMoveAndUp",value:function(t){var e=this,n=this._events,r=this._handler;w(t)?(n.touchmove=r.on(document.body,"touchmove",(function(t){return e._mouseMove(t)}),{passive:!1}),n.touchend=r.on(document.body,"touchend",(function(t){return e._mouseUp(t)})),n.touchcancel=r.on(document.body,"touchcancel",(function(t){return e._mouseUp(t)}))):(n.mousemove=r.on(document.body,"mousemove",(function(t){return e._mouseMove(t)})),n.mouseup=r.on(document.body,"mouseup",(function(t){return e._mouseUp(t)}))),this._started=!0,this._moved=!1}},{key:"_mouseMove",value:function(t){w(t)||1===C(t)?(this._moved=this._moveInternal(t)||this._moved,b(t)):this._mouseUp(t)}},{key:"_moveInternal",value:function(t){}},{key:"_mouseUp",value:function(t){var e=this,n=this._events,r=this._handler;r.off(n.mousemove),r.off(n.touchmove),r.off(n.mouseup),r.off(n.touchend),r.off(n.touchcancel),this._started=!1,this._upInternal(t),this._moved&&(this._mouseEndPoint=Rt(this._grid,t),setTimeout((function(){e._mouseEndPoint=null}),10))}},{key:"_upInternal",value:function(t){}},{key:"dispose",value:function(){this._handler.dispose()}}]),t}(),Ft=function(t){function e(t,n){var r;return h(this,e),(r=a(this,s(e).call(this,t))).noSelectRange=n,r}return c(e,t),v(e,[{key:"start",value:function(t){var e=this._getTargetCell(t);e&&(At(this._grid,e.col,e.row,t.shiftKey),this._bindMoveAndUp(t),this._cell=e,b(t),lt(t))}},{key:"select",value:function(t){var e=this._getTargetCell(t);e&&(At(this._grid,e.col,e.row,t.shiftKey),this._cell=e)}},{key:"_moveInternal",value:function(t){if(!this.noSelectRange){var e=this._getTargetCell(t);if(!e)return!1;var n=this._cell,r=n.col,o=n.row,i=e.col,a=e.row;if(r===i&&o===a)return!1;var l=this._grid;At(l,i,a,!0);var s=i<r&&0<i?i-1:r<i&&i+1<l.colCount?i+1:i,c=a<o&&0<a?a-1:o<a&&a+1<l.rowCount?a+1:a;return s===i&&c===a||l.makeVisibleCell(s,c),this._cell=e,!0}}},{key:"_getTargetCell",value:function(t){var e=this._grid,n=Rt(e,t);if(!n)return null;var r=e.getCellAt(n.x,n.y);return r.col<0||r.row<0?null:r}}]),e}(Pt),zt=function(t){function e(t){var n;return h(this,e),(n=a(this,s(e).call(this,t)))._targetCol=-1,n}return c(e,t),v(e,[{key:"start",value:function(t,e){var n;n=w(e)?e.changedTouches[0].pageX:e.pageX,this._x=n,this._preX=0,this._bindMoveAndUp(e),this._targetCol=t,this._invalidateAbsoluteLeft=Lt(this._grid,0,t-1),b(e),lt(e)}},{key:"_moveInternal",value:function(t){var e=(w(t)?t.changedTouches[0].pageX:t.pageX)-this._x,n=e-this._preX;this._preX=e;var r=this._grid.getColWidth(this._targetCol),o=_t(this._grid,this._targetCol,r+n);o<10&&n<0&&(o=10),function(t){for(var e=null,n=0;n<t[B].colCount;n++)xt(wt(t,n))&&It(t,n,e||(e=bt(t)))}(this._grid),It(this._grid,this._targetCol,o);var i=Mt(this._grid);return i.left=this._invalidateAbsoluteLeft,pt(this._grid,i),this._grid.fireListeners("resize_column",{col:this._targetCol,width:o}),!0}},{key:"_upInternal",value:function(t){var e=this._grid;e.updateScroll()&&e.invalidate()}}]),e}(Pt);function Wt(t,e){var n=t.type;t.type="",t.value=e,n&&(t.type=n)}var Ht=function(e){function r(e,n,o){var i;h(this,r),(i=a(this,s(r).call(this)))._grid=e,i._scrollable=o;var l,c=i._handler=new L,u=i._input=document.createElement("input");u.classList.add("grid-focus-control"),u.readOnly=!0,n.appendChild(u),c.on(u,"compositionstart",(function(t){u.classList.add("composition"),u.style.font=e.font||"16px sans-serif",i._isComposition=!0,i._compositionEnd&&(clearTimeout(i._compositionEnd),delete i._compositionEnd),e.focus()}));var f=function(){l=u.value,i._isComposition||Wt(u,"")},v=function(){i._isComposition=!1,u.classList.remove("composition"),u.style.font="";var t=u.value;f(),u.readOnly||i.fireListeners("input",t),i._compositionEnd&&(clearTimeout(i._compositionEnd),delete i._compositionEnd)};return c.on(u,"compositionend",(function(t){i._compositionEnd=setTimeout(v)})),c.on(u,"keypress",(function(t){i._isComposition||(!u.readOnly&&t.key&&1===t.key.length&&("c"===t.key&&(t.ctrlKey||t.metaKey)||"v"===t.key&&(t.ctrlKey||t.metaKey)||(i.fireListeners("input",t.key),b(t))),f())})),c.on(u,"keydown",(function(t){if(i._isComposition)i._compositionEnd&&(v(),b(t));else{var e=x(t);i.fireListeners("keydown",e,t),!u.readOnly&&l&&i.fireListeners("input",l),f()}})),c.on(u,"keyup",(function(t){i._isComposition&&i._compositionEnd&&v(),f()})),c.on(u,"input",(function(t){f()})),g.IE&&c.on(document,"keydown",(function(t){if(t.target===u){var e=x(t);e===H&&t.ctrlKey?(Wt(u,"dummy"),u.select(),setTimeout((function(){Wt(u,"")}),100)):e===N&&t.ctrlKey&&u.readOnly&&(u.readOnly=!1,setTimeout((function(){u.readOnly=!0,Wt(u,"")}),10))}})),g.Edge&&c.once(document,"keydown",(function(t){if(p(n,t.target)){var r=document.createElement("input");e.getElement().appendChild(r),r.focus(),u.focus(),r.parentElement.removeChild(r)}})),c.on(document,"paste",(function(e){if(p(n,e.target)){var r=void 0;if(g.IE)r=t.clipboardData.getData("Text");else{var o=e.clipboardData;o.items?r=o.getData("text/plain"):-1!==Array.prototype.indexOf.call(o.types,"text/plain")&&(r=o.getData("Text"))}d(r)&&r.length&&i.fireListeners("paste",{value:r,event:e})}})),c.on(document,"copy",(function(e){if(!i._isComposition&&p(n,e.target)){Wt(u,"");var r=y.find(i.fireListeners("copy"),d);d(r)&&(b(e),g.IE?t.clipboardData.setData("Text",r):e.clipboardData.setData("text/plain",r))}})),c.on(u,"focus",(function(t){i.fireListeners("focus",t)})),c.on(u,"blur",(function(t){i.fireListeners("blur",t)})),i}return c(r,e),v(r,[{key:"onKeyDownMove",value:function(t){var e=this;this._handler.on(this._input,"keydown",(function(n){if(!e._isComposition){var r=x(n);r!==P&&r!==F&&r!==z&&r!==W&&r!==D&&r!==M||t(n)}}))}},{key:"onKeyDown",value:function(t){return this.listen("keydown",t)}},{key:"onInput",value:function(t){return this.listen("input",t)}},{key:"onCopy",value:function(t){return this.listen("copy",t)}},{key:"onPaste",value:function(t){return this.listen("paste",t)}},{key:"onFocus",value:function(t){return this.listen("focus",t)}},{key:"onBlur",value:function(t){return this.listen("blur",t)}},{key:"focus",value:function(){this._input.focus()}},{key:"setFocusRect",value:function(t){var e=this._input,n=this._scrollable.calcTop(t.top);e.style.top="".concat((n-T.getScrollBarSize()).toFixed(),"px"),e.style.left="".concat(t.left.toFixed(),"px"),e.style.width="".concat(t.width.toFixed(),"px"),e.style.height="".concat(t.height.toFixed(),"px")}},{key:"resetInputStatus",value:function(){for(var t=this._input,e=t.classList.contains("composition"),n=t.attributes,r=[],o=0,i=n.length;o<i;o++){var a=n[o];this._inputStatus.hasOwnProperty(a.nodeName)||r.push(a.name)}for(var l in r.forEach((function(e){t.removeAttribute(e)})),this._inputStatus)t.setAttribute(l,this._inputStatus[l]);e?(t.classList.add("composition"),t.style.font=this._grid.font||"16px sans-serif"):t.classList.remove("composition")}},{key:"storeInputStatus",value:function(){for(var t=this._input,e=this._inputStatus={},n=t.attributes,r=0,o=n.length;r<o;r++){var i=n[r];e[i.name]=i.value}}},{key:"setDefaultInputStatus",value:function(){}},{key:"dispose",value:function(){n(s(r.prototype),"dispose",this).call(this),this._handler.dispose()}},{key:"editMode",set:function(t){this._input.readOnly=!t},get:function(){return!this._input.readOnly}},{key:"input",get:function(){return this._input}}]),r}(k),Nt=function(t){function e(t){var n;return h(this,e),(n=a(this,s(e).call(this)))._grid=t,n._sel={col:0,row:0},n._focus={col:0,row:0},n._start={col:0,row:0},n._end={col:0,row:0},n}return c(e,t),v(e,[{key:"_setSelectCell",value:function(t,e){var n=this;this._wrapFireSelectedEvent((function(){n._sel={col:t,row:e},n._start={col:t,row:e}}))}},{key:"_setFocusCell",value:function(t,e,n){var r=this;this._wrapFireSelectedEvent((function(){n||r._setSelectCell(t,e),r._focus={col:t,row:e},r._end={col:t,row:e}}))}},{key:"_wrapFireSelectedEvent",value:function(t){if(this._isWraped)t();else{this._isWraped=!0;try{var e={col:this._sel.col,row:this._sel.row,selected:!1};t();var n={col:this._sel.col,row:this._sel.row,selected:!0,before:{col:e.col,row:e.row}};e.after={col:n.col,row:n.row},this.fireListeners(q,e),this.fireListeners(q,n)}finally{this._isWraped=!1}}}},{key:"_updateGridRange",value:function(){for(var t=this._grid,e=t.rowCount,n=t.colCount,r=[this._sel,this._focus,this._start,this._end],o=!1,i=0;i<r.length;i++)if(n<=r[i].col||e<=r[i].row){o=!0;break}return!!o&&(this._wrapFireSelectedEvent((function(){r.forEach((function(t){t.col=Math.min(n-1,t.col),t.row=Math.min(e-1,t.row)}))})),!0)}},{key:"range",get:function(){var t=this._start,e=this._end,n=Math.min(t.col,e.col),r=Math.min(t.row,e.row),o=Math.max(t.col,e.col),i=Math.max(t.row,e.row);return{start:{col:n,row:r},end:{col:o,row:i},inCell:function(t,e){return n<=t&&t<=o&&r<=e&&e<=i}}}},{key:"focus",get:function(){var t=this._focus;return{col:t.col,row:t.row}}},{key:"select",get:function(){var t=this._sel;return{col:t.col,row:t.row}},set:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._wrapFireSelectedEvent((function(){var n=e.col,r=void 0===n?0:n,o=e.row,i=void 0===o?0:o;t._setSelectCell(r,i),t._setFocusCell(r,i,!0)}))}}]),e}(k),Ut=function(){function t(){h(this,t),this._layers={}}return v(t,[{key:"addDraw",value:function(t,e){(this._layers[t]||(this._layers[t]=new Vt(t))).addDraw(e)}},{key:"draw",value:function(t){var e=[];for(var n in this._layers)e.push(this._layers[n]);e.sort((function(t,e){return t.level-e.level})),e.forEach((function(e){return e.draw(t)}))}}]),t}(),Vt=function(){function t(e){h(this,t),this._level=e,this._list=[]}return v(t,[{key:"addDraw",value:function(t){this._list.push(t)}},{key:"draw",value:function(t){this._list.forEach((function(e){t.save();try{e(t)}finally{t.restore()}}))}},{key:"level",get:function(){return this._level}}]),t}(),jt=function(){function t(e,n,r,o,i,a,l,s){h(this,t),this._col=e,this._row=n,this._mode=0,this._ctx=r,this._rect=o,this._drawRect=i,this._drawing=a,this._selection=l,this._drawLayers=s,this._childContexts=[]}return v(t,[{key:"cancel",value:function(){this._cancel=!0,this._childContexts.forEach((function(t){t.cancel()}))}},{key:"getSelectState",value:function(){var t=this._selection.select;return{selected:t.col===this._col&&t.row===this._row,selection:this._selection.range.inCell(this._col,this._row)}}},{key:"getContext",value:function(){return 0===this._mode?this._ctx:this._grid._getInitContext()}},{key:"getRect",value:function(){return 0===this._mode?this._rect:this._rect?this._rect:this._grid.getCellRelativeRect(this._col,this._row)}},{key:"setRect",value:function(t){this._rect=t}},{key:"getDrawRect",value:function(){if(this._cancel)return null;if(0===this._mode)return this._drawRect;if(this._isOutOfRange())return null;var t=this._grid.getCellRect(this._col,this._row);return this._toRelativeDrawRect(t)}},{key:"_isOutOfRange",value:function(){var t=this._grid,e=t.colCount,n=t.rowCount;return e<=this._col||n<=this._row}},{key:"toCurrentContext",value:function(){if(0===this._mode)return this;var e=this._grid.getCellRect(this._col,this._row),n=Dt(this._grid,e),r=this._isOutOfRange()?null:this._toRelativeDrawRect(e),o=new t(this._col,this._row,this.getContext(),n,r,this.drawing,this._selection,this._drawLayers);return o.toCurrentContext=this.toCurrentContext.bind(this),this._childContexts.push(o),this._cancel&&o.cancel(),o}},{key:"addLayerDraw",value:function(t,e){this._drawLayers.addDraw(t,e)}},{key:"_toRelativeDrawRect",value:function(t){var e=Mt(this._grid),n=t.copy();if(!n.intersection(e))return null;var r=this._grid.isFrozenCell(this._col,this._row);if(this._grid.frozenColCount>=0&&(!r||!r.col)){var o=this._grid.getCellRect(this._grid.frozenColCount-1,this._row);n=E.bounds(Math.max(n.left,o.right),n.top,n.right,n.bottom)}if(this._grid.frozenRowCount>=0&&(!r||!r.row)){var i=this._grid.getCellRect(this._col,this._grid.frozenRowCount-1);n=E.bounds(n.left,Math.max(n.top,i.bottom),n.right,n.bottom)}return n.intersection(e)?(n.offsetLeft(-e.left),n.offsetTop(-e.top),n):null}},{key:"_delayMode",value:function(t,e){this._mode=1,this._ctx=null,this._rect=null,this._drawRect=null,this._grid=t,this._onTerminate=e}},{key:"terminate",value:function(){0!==this._mode&&this._onTerminate()}},{key:"drawing",get:function(){return 0!==this._mode||this._drawing}},{key:"row",get:function(){return this._row}},{key:"col",get:function(){return this._col}}]),t}(),Gt=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.rowCount,o=void 0===r?10:r,i=n.colCount,c=void 0===i?10:i,u=n.frozenColCount,f=void 0===u?0:u,d=n.frozenRowCount,v=void 0===d?0:d,g=n.defaultRowHeight,p=void 0===g?40:g,y=n.defaultColWidth,_=void 0===y?80:y,m=n.font,w=n.underlayBackgroundColor,C=n.parentElement,x=(n.noSelect,n.noSelectRange),b=n.supportOpacity;h(this,e),(t=a(this,s(e).call(this))).supportOpacity=b;var k,E=t[B]={};return T.initDocument(),E.element=((k=document.createElement("div")).classList.add("cheetah-grid"),k),E.scrollable=new I,E.handler=new L,E.selection=new Nt(l(t)),E.focusControl=new Ht(l(t),E.scrollable.getElement(),E.scrollable),E.canvas=O.transform(document.createElement("canvas")),E.context=E.canvas.getContext("2d",{alpha:!1}),E.rowCount=o,E.colCount=c,E.frozenColCount=f,E.frozenRowCount=v,E.defaultRowHeight=p,E.defaultColWidth=_,E.font=m,E.underlayBackgroundColor=w,E.rowHeightsMap=new S,E.colWidthsMap=new S,E.colWidthsLimit={},E.calcWidthContext={_:E,get full(){return this._.canvas.width},get em(){return R(this._.context,this._.font).width}},E.columnResizer=new zt(l(t)),E.cellSelector=new Ft(l(t),x),E.drawCells={},E.cellTextOverflows={},E.focusedGrid=!1,E.element.appendChild(E.canvas),E.element.appendChild(E.scrollable.getElement()),t.updateScroll(),C?(C.appendChild(E.element),t.updateSize()):t.updateSize(),Ot(l(t)),t}return c(e,t),v(e,null,[{key:"EVENT_TYPE",get:function(){return at}}]),v(e,[{key:"getElement",value:function(){return this[B].element}},{key:"focus",value:function(){var t=this[B].selection.select,e=t.col,n=t.row;this.focusCell(e,n)}},{key:"hasFocusGrid",value:function(){return this[B].focusedGrid}},{key:"configure",value:function(t,e){var n=this[B].config||(this[B].config={});return d(e)&&(n[t]=e),n[t]}},{key:"updateSize",value:function(){var t=this[B].canvas;t.style.width="",t.style.height="";var e=Math.floor(t.offsetWidth||t.parentElement.offsetWidth-T.getScrollBarSize()),n=Math.floor(t.offsetHeight||t.parentElement.offsetHeight-T.getScrollBarSize());t.width=e,t.height=n,t.style.width="".concat(e,"px"),t.style.height="".concat(n,"px");var r=this[B].selection.select;this[B].focusControl.setFocusRect(this.getCellRect(r.col,r.row))}},{key:"updateScroll",value:function(){var t,e=this[B].scrollable,n=function(t,e){var n=t.getScrollHeightInternal(e);if(d(n))return n;var r=t[B].defaultRowHeight*t[B].rowCount;return t[B].rowHeightsMap.each(0,t[B].rowCount-1,(function(e){r+=e-t[B].defaultRowHeight})),r}(this),r=Lt(t=this,0,t[B].colCount-1);return(n!==e.scrollHeight||r!==e.scrollWidth)&&(e.setScrollSize(r,n),this[B].scroll={left:e.scrollLeft,top:e.scrollTop},!0)}},{key:"getRowHeight",value:function(t){return St(this,t)}},{key:"setRowHeight",value:function(t,e){!function(t,e,n){t[B].rowHeightsMap.put(e,n)}(this,t,e)}},{key:"getColWidth",value:function(t){return Et(this,t)}},{key:"setColWidth",value:function(t,e){It(this,t,e)}},{key:"getMaxColWidth",value:function(t){var e=this[B].colWidthsLimit[t];return e&&e.max||void 0}},{key:"setMaxColWidth",value:function(t,e){(this[B].colWidthsLimit[t]||(this[B].colWidthsLimit[t]={})).max=e}},{key:"getMinColWidth",value:function(t){var e=this[B].colWidthsLimit[t];return e&&e.min||void 0}},{key:"setMinColWidth",value:function(t,e){(this[B].colWidthsLimit[t]||(this[B].colWidthsLimit[t]={})).min=e}},{key:"getCellRect",value:function(t,e){var n=this.isFrozenCell(t,e),r=Lt(this,0,t-1),o=Et(this,t);n&&n.col&&(r+=this[B].scroll.left);var i=Tt(this,0,e-1),a=St(this,e);return n&&n.row&&(i+=this[B].scroll.top),new E(r,i,o,a)}},{key:"getCellRelativeRect",value:function(t,e){return Dt(this,this.getCellRect(t,e))}},{key:"getCellsRect",value:function(t,e,n,r){var o=this.isFrozenCell(t,e),i=this.isFrozenCell(n,r),a=Lt(this,0,t-1),l=Lt(this,t,n);if(o&&o.col){var s=this[B].scroll.left;a+=s,i&&i.col||(l-=s,l=Math.max(l,Lt(this,t,this.frozenColCount-1)))}var c=Tt(this,0,e-1),u=Tt(this,e,r);if(o&&o.row){var f=this[B].scroll.top;c+=f,i&&i.row||(u-=f,u=Math.max(u,Lt(this,e,this.frozenRowCount-1)))}return new E(a,c,l,u)}},{key:"isFrozenCell",value:function(t,e){var n=this[B],r=n.frozenRowCount,o=n.frozenColCount,i=r>0&&e<r,a=o>0&&t<o;return i||a?{row:i,col:a}:null}},{key:"getRowAt",value:function(t){var e=ut(this,t);if(e)return e.row;var n=st(this,t);return n?n.row:-1}},{key:"getColAt",value:function(t){var e=ft(this,t);if(e)return e.col;var n=ct(this,t);return n?n.col:-1}},{key:"getCellAt",value:function(t,e){return{row:this.getRowAt(e),col:this.getColAt(t)}}},{key:"makeVisibleCell",value:function(t,e){var n=this.isFrozenCell(t,e);if(!(n&&n.col&&n.row)){var r=this.getCellRect(t,e),o=function(t){var e=0;t[B].frozenColCount>0&&(e=dt(t).width);var n=0;return t[B].frozenRowCount>0&&(n=ht(t).height),new E(t[B].scrollable.scrollLeft+e,t[B].scrollable.scrollTop+n,t[B].canvas.width-e,t[B].canvas.height-n)}(this);if(!o.contains(r)){var i=this[B].scrollable;n&&n.col||(r.left<o.left?i.scrollLeft-=o.left-r.left:o.right<r.right&&(i.scrollLeft-=o.right-r.right)),n&&n.row||(r.top<o.top?i.scrollTop-=o.top-r.top:o.bottom<r.bottom&&(i.scrollTop-=o.bottom-r.bottom))}}}},{key:"setFocusCursor",value:function(t,e){var n=this[B].focusControl;n.editMode&&n.resetInputStatus(),n.setFocusRect(this.getCellRect(t,e));var r=this[B].selection.select,o=r.col,i=r.row,a=this.fireListeners("editableinput_cell",{col:o,row:i}),l=y.findIndex(a,(function(t){return!!t}))>=0;n.editMode=l,l&&(n.storeInputStatus(),n.setDefaultInputStatus(),this.fireListeners("modify_status_editableinput_cell",{col:o,row:i,input:n.input}))}},{key:"focusCell",value:function(t,e){this.setFocusCursor(t,e),this[B].focusControl.focus()}},{key:"invalidateCell",value:function(t,e){this.invalidateGridRect(t,e)}},{key:"invalidateGridRect",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,o=this.getOffsetInvalidateCells();o>0&&(t-=o,e-=o,n+=o,r+=o);var i=Mt(this),a=this.getCellsRect(t,e,n,r),l=i.intersection(a);if(l){var s=this[B],c=s.frozenColCount,u=s.frozenRowCount;if(c>0&&n>=c){var f=dt(this);f.intersection(l)&&(l.left=Math.min(f.right-1,f.right))}if(u>0&&r>=u){var h=ht(this);h.intersection(l)&&(l.top=Math.min(h.bottom-1,l.top))}pt(this,l)}}},{key:"invalidate",value:function(){pt(this,Mt(this))}},{key:"getCopyCellValue",value:function(t,e){}},{key:"onDrawCell",value:function(t,e,n){}},{key:"getCellOverflowText",value:function(t,e){var n="".concat(t,":").concat(e);return this[B].cellTextOverflows[n]||null}},{key:"setCellOverflowText",value:function(t,e,n){var r="".concat(t,":").concat(e);n?this[B].cellTextOverflows[r]="string"==typeof n?n.trim():n:delete this[B].cellTextOverflows[r]}},{key:"addDisposable",value:function(t){if(!t||!t.dispose||"function"!=typeof t.dispose)throw new Error("not disposable!");(this[B].disposables=this[B].disposables||[]).push(t)}},{key:"dispose",value:function(){n(s(e.prototype),"dispose",this).call(this);var t=this[B];t.handler.dispose(),t.scrollable.dispose(),t.focusControl.dispose(),t.columnResizer.dispose(),t.cellSelector.dispose(),t.disposables&&(t.disposables.forEach((function(t){return t.dispose()})),t.disposables=null);var r=t.element.parentElement;r&&r.removeChild(t.element)}},{key:"getAttachCellArea",value:function(t,e){return{element:this.getElement(),rect:Dt(this,this.getCellRect(t,e))}}},{key:"bindEventsInternal",value:function(){}},{key:"getTargetRowAtInternal",value:function(t){}},{key:"getRowsHeightInternal",value:function(t,e){}},{key:"getRowHeightInternal",value:function(t){}},{key:"getScrollHeightInternal",value:function(t){}},{key:"getMoveLeftColByKeyDownInternal",value:function(t){var e=t.col;return t.row,e-1}},{key:"getMoveRightColByKeyDownInternal",value:function(t){var e=t.col;return t.row,e+1}},{key:"getMoveUpRowByKeyDownInternal",value:function(t){return t.col,t.row-1}},{key:"getMoveDownRowByKeyDownInternal",value:function(t){return t.col,t.row+1}},{key:"getOffsetInvalidateCells",value:function(){return 0}},{key:"_getInitContext",value:function(){var t=this[B].context;return t.fillStyle="white",t.strokeStyle="black",t.textAlign="left",t.textBaseline="top",t.lineWidth=1,t.font=this.font||"16px sans-serif",t}},{key:"canvas",get:function(){return this[B].canvas}},{key:"selection",get:function(){return this[B].selection}},{key:"rowCount",get:function(){return this[B].rowCount},set:function(t){if(this[B].rowCount=t,this.updateScroll(),this[B].selection._updateGridRange()){var e=this[B].selection.focus,n=e.col,r=e.row;this.makeVisibleCell(n,r),this.setFocusCursor(n,r)}}},{key:"colCount",get:function(){return this[B].colCount},set:function(t){if(this[B].colCount=t,this.updateScroll(),this[B].selection._updateGridRange()){var e=this[B].selection.focus,n=e.col,r=e.row;this.makeVisibleCell(n,r),this.setFocusCursor(n,r)}}},{key:"frozenColCount",get:function(){return this[B].frozenColCount},set:function(t){this[B].frozenColCount=t}},{key:"frozenRowCount",get:function(){return this[B].frozenRowCount},set:function(t){this[B].frozenRowCount=t}},{key:"defaultRowHeight",get:function(){return this[B].defaultRowHeight},set:function(t){this[B].defaultRowHeight=t}},{key:"defaultColWidth",get:function(){return this[B].defaultColWidth},set:function(t){this[B].defaultColWidth=t}},{key:"font",get:function(){return this[B].font},set:function(t){this[B].font=t}},{key:"underlayBackgroundColor",get:function(){return this[B].underlayBackgroundColor},set:function(t){this[B].underlayBackgroundColor=t}}]),e}(k);e.exports=Gt},function(t,e){t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n=function(t,e){var n,r=t[1]||"",o=t[3];if(!o)return r;if(e&&"function"==typeof btoa){var i=(n=o,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),a=o.sources.map((function(t){return"/*# sourceURL="+o.sourceRoot+t+" */"}));return[r].concat(a).concat([i]).join("\n")}return[r].join("\n")}(e,t);return e[2]?"@media "+e[2]+"{"+n+"}":n})).join("")},e.i=function(t,n){"string"==typeof t&&(t=[[null,t,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];"number"==typeof i&&(r[i]=!0)}for(o=0;o<t.length;o++){var a=t[o];"number"==typeof a[0]&&r[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),e.push(a))}},e}},function(e,n,r){var o,i,a={},l=(o=function(){return t&&document&&document.all&&!t.atob},function(){return void 0===i&&(i=o.apply(this,arguments)),i}),s=function(t,e){return e?e.querySelector(t):document.querySelector(t)},c=function(e){var n={};return function(e,r){if("function"==typeof e)return e();if(void 0===n[e]){var o=s.call(this,e,r);if(t.HTMLIFrameElement&&o instanceof t.HTMLIFrameElement)try{o=o.contentDocument.head}catch(t){o=null}n[e]=o}return n[e]}}(),u=null,f=0,h=[],d=r(73);function v(t,e){for(var n=0;n<t.length;n++){var r=t[n],o=a[r.id];if(o){o.refs++;for(var i=0;i<o.parts.length;i++)o.parts[i](r.parts[i]);for(;i<r.parts.length;i++)o.parts.push(C(r.parts[i],e))}else{var l=[];for(i=0;i<r.parts.length;i++)l.push(C(r.parts[i],e));a[r.id]={id:r.id,refs:1,parts:l}}}}function p(t,e){for(var n=[],r={},o=0;o<t.length;o++){var i=t[o],a=e.base?i[0]+e.base:i[0],l={css:i[1],media:i[2],sourceMap:i[3]};r[a]?r[a].parts.push(l):n.push(r[a]={id:a,parts:[l]})}return n}function y(t,e){var n=c(t.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=h[h.length-1];if("top"===t.insertAt)r?r.nextSibling?n.insertBefore(e,r.nextSibling):n.appendChild(e):n.insertBefore(e,n.firstChild),h.push(e);else if("bottom"===t.insertAt)n.appendChild(e);else{if("object"!==g(t.insertAt)||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var o=c(t.insertAt.before,n);n.insertBefore(e,o)}}function _(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var e=h.indexOf(t);e>=0&&h.splice(e,1)}function m(t){var e=document.createElement("style");if(void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce){var n=r.nc;n&&(t.attrs.nonce=n)}return w(e,t.attrs),y(t,e),e}function w(t,e){Object.keys(e).forEach((function(n){t.setAttribute(n,e[n])}))}function C(t,e){var n,r,o,i;if(e.transform&&t.css){if(!(i="function"==typeof e.transform?e.transform(t.css):e.transform.default(t.css)))return function(){};t.css=i}if(e.singleton){var a=f++;n=u||(u=m(e)),r=k.bind(null,n,a,!1),o=k.bind(null,n,a,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(t){var e=document.createElement("link");return void 0===t.attrs.type&&(t.attrs.type="text/css"),t.attrs.rel="stylesheet",w(e,t.attrs),y(t,e),e}(e),r=I.bind(null,n,e),o=function(){_(n),n.href&&URL.revokeObjectURL(n.href)}):(n=m(e),r=E.bind(null,n),o=function(){_(n)});return r(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;r(t=e)}else o()}}e.exports=function(t,e){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!==("undefined"==typeof document?"undefined":g(document)))throw new Error("The style-loader cannot be used in a non-browser environment");(e=e||{}).attrs="object"===g(e.attrs)?e.attrs:{},e.singleton||"boolean"==typeof e.singleton||(e.singleton=l()),e.insertInto||(e.insertInto="head"),e.insertAt||(e.insertAt="bottom");var n=p(t,e);return v(n,e),function(t){for(var r=[],o=0;o<n.length;o++){var i=n[o];(l=a[i.id]).refs--,r.push(l)}for(t&&v(p(t,e),e),o=0;o<r.length;o++){var l;if(0===(l=r[o]).refs){for(var s=0;s<l.parts.length;s++)l.parts[s]();delete a[l.id]}}}};var x,b=(x=[],function(t,e){return x[t]=e,x.filter(Boolean).join("\n")});function k(t,e,n,r){var o=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=b(e,o);else{var i=document.createTextNode(o),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(i,a[e]):t.appendChild(i)}}function E(t,e){var n=e.css,r=e.media;if(r&&t.setAttribute("media",r),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}function I(t,e,n){var r=n.css,o=n.sourceMap,i=void 0===e.convertToAbsoluteUrls&&o;(e.convertToAbsoluteUrls||i)&&(r=d(r)),o&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */");var a=new Blob([r],{type:"text/css"}),l=t.href;t.href=URL.createObjectURL(a),l&&URL.revokeObjectURL(l)}},function(t,e,n){var r={};function o(t,e,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},i=o.offset,a=void 0===i?0:i,l=o.padding,s=(l=void 0===l?{}:l).left,c=void 0===s?0:s,u=l.right,f=void 0===u?0:u,h=l.top,d=void 0===h?0:h,v=l.bottom,g=void 0===v?0:v,p=t.textAlign||"left",y=t.textBaseline||"middle";t.textAlign=p,t.textBaseline=y;var _=e.left+a+c;"right"===p||"end"===p?_=e.right-n-a-f:"center"===p&&(_=e.left+(e.width-n+c-f)/2);var m=e.top+a+d;return"bottom"===y||"alphabetic"===y||"ideographic"===y?m=e.bottom-r-a-g:"middle"===y&&(m=e.top+(e.height-r+d-g)/2),{x:_,y:m}}t.exports={calcBasePosition:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.offset,i=void 0===r?0:r,a=n.padding,l=(a=void 0===a?{}:a).left,s=void 0===l?0:l,c=a.right,u=void 0===c?0:c,f=a.top,h=void 0===f?0:f,d=a.bottom;return o(t,e,0,0,{offset:i,padding:{left:s,right:u,top:h,bottom:void 0===d?0:d}})},calcStartPosition:o,getFontSize:function(t,e){if(r[e])return r[e];var n=t.font;try{t.font=e||t.font;var o=t.measureText("あ").width;return r[e]={width:o,height:o}}finally{t.font=n}}}},function(t,e,n){var r=n(42),o=n(0),i=o.isPromise,a=o.isDef,l=o.obj.setReadonly,s=n(22),c=n(4),u=n(1).COLUMN_FADEIN_STATE_ID;function f(t){return t[u]||l(t,u,{}),t[u]}function d(t,e,n,r,o,i){return function(a){var l=f(t),s="".concat(e,":").concat(n);1===a?delete l[s]:l[s]={opacity:a},i(),o();var c=l[s];if(c){var u=r.getContext();u.globalAlpha=1-c.opacity;try{i()}finally{u.globalAlpha=1}}}}var g=function(t,e,n,r,o,i){var a=f(t),l=[d(t,e,n,r,o,i)];a.activeFadeins=l,s(500,(function(t){l.forEach((function(e){return e(t)})),1===t&&delete a.activeFadeins}))},p=function(t,e,n,r,o,i){var a=f(t);a.activeFadeins?a.activeFadeins.push(d(t,e,n,r,o,i)):o()},y=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,t),this.onDrawCell=this.onDrawCell.bind(this),this._fadeinWhenCallbackInPromise=e.fadeinWhenCallbackInPromise}return v(t,[{key:"onDrawCell",value:function(t,e,n,o){var l=this,s=e.style,c=e.getRecord,f=e.drawCellBase,h=o.getGridCanvasHelper();f();var d,v=c();if(i(v)?d=v.then((function(){return t})):i(t)&&(d=t),d){var y=Date.now();return d.then((function(t){if(n.toCurrentContext().getDrawRect()){var u=Date.now()-y,d=function(){var a=n.toCurrentContext();if(a.getDrawRect()){var u=c();if(!i(u)){var f=r.of(s,u,l.StyleClass);l.drawInternal(l.convertInternal(t),a,f,h,o,e),l.drawMessageInternal(e.getMessage(),n,f,h,o,e)}}};if(function(t,e){return a(t._fadeinWhenCallbackInPromise)?t._fadeinWhenCallbackInPromise:!!e.configure("fadeinWhenCallbackInPromise")}(l,o)){var v=n.col,_=n.row;u<80?p(o,v,_,n,d,f):g(o,v,_,n,d,f)}else d()}}))}var _=r.of(s,v,this.StyleClass);this.drawInternal(this.convertInternal(t),n,_,h,o,e),this.drawMessageInternal(e.getMessage(),n,_,h,o,e);var m=n.col,w=n.row,C="".concat(m,":").concat(w),x=o[u]&&o[u][C];if(x){var b=n.getContext();b.globalAlpha=1-x.opacity;try{f()}finally{b.globalAlpha=1}}return null}},{key:"clone",value:function(){return new t(this)}},{key:"convertInternal",value:function(t){return a(t)?t:""}},{key:"drawInternal",value:function(t,e,n,r,o,i){}},{key:"drawMessageInternal",value:function(t,e,n,r,o,i){i.messageHandler.drawCellMessage(t,e,n,r,o,i)}},{key:"bindGridEvent",value:function(t,e,n){return[]}},{key:"StyleClass",get:function(){return c}}]),t}();t.exports=y},function(e,n,r){(function(n){var o=r(0).isDef;function i(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.classList,i=r.text,a=r.html,l=document.createElement(t);return o&&(n(o)?(e=l.classList).add.apply(e,f(o)):l.classList.add(o)),i?l.textContent=i:a&&(l.innerHTML=a),l}function a(t){return!(!t.nodeType||!t.nodeName)}function l(t){if(a(t))return t;var e=i("div",{html:t});return Array.prototype.slice.call(e.childNodes)}function s(e){if(t.jQuery&&e instanceof t.jQuery)return Array.prototype.map.call(e,(function(t){return t}));if(n(e))return e.map(l);var r=l(e);return n(r)?r:[l(e)]}function c(t){return o(t.tabIndex)&&t.tabIndex>-1}e.exports={createElement:i,empty:function(t){for(var e;e=t.firstChild;)t.removeChild(e)},isNode:a,toNode:l,toNodeList:s,appendHtml:function(t,e){s(e).forEach((function(e){t.appendChild(e)}))},disableFocus:function t(e){e.dataset.disableBeforeTabIndex=e.tabIndex,e.tabIndex=-1,Array.prototype.slice.call(e.children,0).forEach(t)},enableFocus:function t(e){"disableBeforeTabIndex"in e.dataset&&(e.tabIndex=e.dataset.disableBeforeTabIndex),Array.prototype.slice.call(e.children,0).forEach(t)},isFocusable:c,findPrevSiblingFocusable:function(t){for(var e=t.previousSibling;e&&!c(e);)e=e.previousSibling;return e},findNextSiblingFocusable:function(t){for(var e=t.nextSibling;e&&!c(e);)e=e.nextSibling;return e}}}).call(this,r(2).Array_isArray)},function(t,e,n){var r=n(4),o=n(9),i=n(17),l=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,e,n,r,o,a){var l=a.drawCellBase,s=a.getIcon,c=n.textAlign,u=n.textBaseline,f=n.color,h=n.font,d=n.bgColor,v=n.padding,g=n.textOverflow;d&&l({bgColor:d}),r.testFontLoad(h,t,e),i.loadIcons(s(),e,r,(function(e,n){r.text(t,n,{textAlign:c,textBaseline:u,color:f,font:h,padding:v,textOverflow:g,icons:e})}))}},{key:"StyleClass",get:function(){return r}}]),e}(o);t.exports=l},function(t,e,n){var r=n(55),o=n(27),i=n(108),a=n(56),l={get BaseStyle(){return r},get Style(){return o},get SortHeaderStyle(){return i},get CheckHeaderStyle(){return a},of:function(t,e){return t?t instanceof o?t:"function"==typeof t?l.of(t(),e):new e(t):e.DEFAULT}};t.exports=l},function(t,e,n){var r=n(0),o=r.isDef,i=r.str,a=i.genChars,l=i.genWords;function s(t,e){return t.measureText(e).width}function c(t,e,n,r,o){for(var i=[],a=n.next(),l=0;l<r&&null!==a;l++,a=n.next())i.push(a);var c=s(t,i.join(""));if(c>o)for(;i.length&&!((c-=s(t,i.pop()))<=o););else if(c<o)for(;null!==a;){var f=s(t,a);if(c+f>o)break;i.push(a),c+=f,a=n.next()}var h=i.join("").replace(/\s+$/,""),d=e.slice(h.length).replace(/^\s+/,"");return{before:h?new u(h):null,after:d?new u(d):null}}var u=function(){function t(e){h(this,t),this._content=o(e)?"".concat(e):""}return v(t,[{key:"width",value:function(t){return s(t.ctx,this._content)}},{key:"font",value:function(){return null}},{key:"color",value:function(){return null}},{key:"canDraw",value:function(){return!0}},{key:"onReady",value:function(t){}},{key:"draw",value:function(t){var e=t.ctx,n=t.canvashelper,r=t.rect,o=t.offset,i=t.offsetLeft,a=t.offsetRight,l=t.offsetTop,s=t.offsetBottom;n.fillTextRect(e,this._content,r.left,r.top,r.width,r.height,{offset:o+1,padding:{left:i,right:a,top:l,bottom:s}})}},{key:"canBreak",value:function(){return!!this._content}},{key:"splitIndex",value:function(e){for(var n=this._content,r=a(n),o=[],i=r.next(),l=0;l<e&&null!==i;l++,i=r.next())o.push(i);var s=o.join(""),c=n.slice(s.length);return{before:s?new t(s):null,after:c?new t(c):null}}},{key:"breakWord",value:function(t,e){var n=this._content,r=this.width({ctx:t}),o=Math.floor(this._content.length*e/r);return c(t,n,l(n),o,e)}},{key:"breakAll",value:function(t,e){var n=this._content,r=this.width({ctx:t}),o=Math.floor(this._content.length*e/r);return c(t,n,a(n),o,e)}},{key:"toString",value:function(){return this._content}}]),t}();t.exports=u},function(t,e,n){var r=n(0),o=r.isDef,i=r.obj.each,a=n(1).get(),l=1,s=function(){function t(){h(this,t),this[a]={},this[a].listeners={},this[a].listenerData={}}return v(t,[{key:"listen",value:function(t,e){var n=this,r=this[a].listeners[t]||(this[a].listeners[t]=[]);r.push(e);var o=l++;return this[a].listenerData[o]={type:t,listener:e,remove:function(){delete n[a].listenerData[o];var i=r.indexOf(e);r.splice(i,1),n[a].listeners[t].length||delete n[a].listeners[t]}},o}},{key:"unlisten",value:function(t){this[a].listenerData[t].remove()}},{key:"addEventListener",value:function(t,e){this.listen(t,e)}},{key:"removeEventListener",value:function(t,e){var n=this;i(this[a].listenerData,(function(r,o){r.type===t&&r.listener===e&&n.unlisten(o)}))}},{key:"hasListeners",value:function(t){return!!this[a].listeners[t]}},{key:"fireListeners",value:function(t){for(var e=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var l=this[a].listeners[t];return l?l.map((function(t){return t.call.apply(t,[e].concat(r))})).filter(o):[]}},{key:"dispose",value:function(){delete this[a]}}]),t}();t.exports=s},function(t,e,n){var r=n(0).isPromise;function o(t,e,n){if("function"==typeof t){var o=e.getRowRecord(n);return!!r(o)||!!t(o)}return!!t}t.exports={isDisabledRecord:o,isReadOnlyRecord:function(t,e,n){return o(t,e,n)}}},function(t,e,n){var r=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._readOnly=n.readOnly,t}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"onChangeReadOnlyInternal",value:function(){}},{key:"readOnly",get:function(){return this._readOnly},set:function(t){this._readOnly=!!t,this.onChangeReadOnlyInternal()}}]),e}(n(21));t.exports=r},function(t,e,n){var r=n(0).isPromise,o=n(25);t.exports={loadIcons:function(t,e,n,i){var a=this;if(t)if(r(t))t.then((function(t){a.loadIcon(t,e.toCurrentContext(),i)})),t=null;else{var l=o.toNormarizeArray(t);l.forEach((function(t){n.testFontLoad(t.font,t.content,e)})),t=l}i(t,e)}}},function(t,e,n){var r=n(12),o=n(0).isDef,i=r.Style,a=function(){function t(){arguments.length>0&&void 0!==arguments[0]&&arguments[0],h(this,t),this.onDrawCell=this.onDrawCell.bind(this)}return v(t,[{key:"onDrawCell",value:function(t,e,n,o){var i=e.style,a=e.drawCellBase;delete e.getRecord,delete e.style;var l=o.getGridCanvasHelper();a(),this.drawInternal(this.convertInternal(t),n,r.of(i,this.StyleClass),l,o,e)}},{key:"convertInternal",value:function(t){return o(t)?t:""}},{key:"drawInternal",value:function(t,e,n,r,o,i){}},{key:"bindGridEvent",value:function(t,e){return[]}},{key:"StyleClass",get:function(){return i}}]),t}();t.exports=a},function(t,e,n){var r=function(){function t(e,n,r,o){h(this,t),this._left=e,this._top=n,this._width=r,this._height=o}return v(t,[{key:"offsetLeft",value:function(t){this._left+=t,this._right=void 0}},{key:"offsetTop",value:function(t){this._top+=t,this._bottom=void 0}},{key:"copy",value:function(){return new t(this.left,this.top,this.width,this.height)}},{key:"intersection",value:function(e){var n=Math.max(this.left,e.left),r=Math.min(this.left+this.width,e.left+e.width);if(n<=r){var o=Math.max(this.top,e.top),i=Math.min(this.top+this.height,e.top+e.height);if(o<=i)return t.bounds(n,o,r,i)}return null}},{key:"contains",value:function(t){return this.left<=t.left&&this.left+this.width>=t.left+t.width&&this.top<=t.top&&this.top+this.height>=t.top+t.height}},{key:"inPoint",value:function(t,e){return this.left<=t&&this.left+this.width>=t&&this.top<=e&&this.top+this.height>=e}},{key:"left",get:function(){return this._left},set:function(t){var e=this.right;this._left=t,this.right=e}},{key:"top",get:function(){return this._top},set:function(t){var e=this.bottom;this._top=t,this.bottom=e}},{key:"width",get:function(){return this._width},set:function(t){this._width=t,this._right=void 0}},{key:"height",get:function(){return this._height},set:function(t){this._height=t,this._bottom=void 0}},{key:"right",get:function(){return void 0!==this._right?this._right:this._right=this.left+this.width},set:function(t){this._right=t,this.width=t-this.left}},{key:"bottom",get:function(){return void 0!==this._bottom?this._bottom:this._bottom=this.top+this.height},set:function(t){this._bottom=t,this.height=t-this.top}}],[{key:"bounds",value:function(e,n,r,o){return new t(e,n,r-e,o-n)}},{key:"max",value:function(e,n){return t.bounds(Math.min(e.left,n.left),Math.min(e.top,n.top),Math.max(e.right,n.right),Math.max(e.bottom,n.bottom))}}]),t}();t.exports=r},function(t,e,n){var r=n(0).array.findIndex,o="p",i="u",a="o",l="n",s="u",c="b",u="n";function f(t){return new Error("calc parse error: ".concat(t))}var h={"*":3,"/":3,"+":2,"-":2};function d(t){return function t(e,n){function d(t){var e=t.pop(),r=t.pop(),o=t.pop();if(!(o&&o.nodeType&&r&&r.type===a&&e&&e.nodeType))throw f(n);return{nodeType:c,left:o,op:r,right:e}}for(var v=[];e.length;){var g=e.shift();if(g.type===o&&"("===g.value)!function(){var i=0,a=r(e,(function(t,e){if(t.type===o&&"("===t.value)i++;else if(t.type===o&&")"===t.value){if(!i)return!0;i--}return!1}));if(-1===a)throw f(n);v.push(t(e.slice(0,a),n)),e.splice(0,a+1)}();else if(g.type===a){if(v.length>=3){var p=v[v.length-2].value;h[g.value]<=h[p]&&v.push(d(v))}v.push(g)}else if(g.type===i){var y=g.value,_=parseFloat(y),m=/[a-z%]+/i.exec(y)[0];v.push({nodeType:s,value:_,unit:m})}else g.type===l&&v.push({nodeType:u,value:parseFloat(g.value)})}for(;v.length>1;)v.push(d(v));return v[0]}(function(t){for(var e,n=t.replace(/calc\(/g,"("),r=/^[-+]?(\d*\.\d+|\d+)[a-z%]+/i,s=/^[-+]?(\d*\.\d+|\d+)/i,c=/^[-+*/]/,u=[];n=n.trim();)if("("===n[0]||")"===n[0])u.push({value:n[0],type:o}),n=n.slice(1);else if(e=r.exec(n))u.push({value:e[0],type:i}),n=n.slice(e[0].length);else if(e=s.exec(n))u.push({value:e[0],type:l}),n=n.slice(e[0].length);else{if(!(e=c.exec(n)))throw f(t);u.push({value:e[0],type:a}),n=n.slice(e[0].length)}return u}(t),t)}function v(t,e){return function t(e,n){if(e.nodeType===c){var r=t(e.left,n),o=t(e.right,n);switch(e.op.value){case"+":return r+o;case"-":return r-o;case"*":return r*o;case"/":return r/o;default:throw new Error("calc error. unknown operator: ".concat(e.op.value))}}else if(e.nodeType===s)switch(e.unit){case"%":return e.value*n.full/100;case"em":return e.value*n.em;case"px":return e.value;default:throw new Error("calc error. unknown unit: ".concat(e.unit))}else if(e.nodeType===u)return e.value;throw new Error("calc error.")}(d(t),e)}t.exports={toPx:function(t,e){return"string"==typeof t?v(t.trim(),e):t-0}}},function(t,e,n){var r=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,t),this._disabled=e.disabled}return v(t,[{key:"clone",value:function(){return new t(this)}},{key:"bindGridEvent",value:function(t,e,n){return[]}},{key:"onChangeDisabledInternal",value:function(){}},{key:"disabled",get:function(){return this._disabled},set:function(t){this._disabled=t,this.onChangeDisabledInternal()}}]),t}();t.exports=r},function(e,n,r){var o=r(0).isNode;function i(t,e,n,r){var o;return t*=3,e*=3,n*=3,r*=3,function(i){var a,l,s,c,u,f;if(i<0||1<i)throw new Error(i);a=o||i;do{a+=.5*(f=i-(t*(s=(l=1-a)*l)*a+n*l*(c=a*a)+(u=c*a)))}while(1e-4<Math.abs(f));return o=a,e*s*a+r*l*c+u}}var a={linear:function(t){return t},easeIn:i(.42,0,1,1),easeOut:i(0,0,.58,1),easeInOut:i(.42,0,.58,1)},l=o?function(){}:t.requestAnimationFrame||setTimeout;function s(){return Date.now()}e.exports=function(e,n,r){var o=s();r?"string"==typeof r&&(r=a[r]):r=a.easeInOut;var i=!1,c=function(t,a){return function c(){var u=s()-o;i?a&&a():u>=e?(n(1),t&&t()):(n(r(u/e)),l(c,1))}},u=function(){i=!0};if(t.Promise){var f=new t.Promise((function(t,e){var r=c(t,e);n(0),r()}));return f.cancel=u,f}var h=c((function(){}),(function(){}));return n(0),h(),{cancel:u}}},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._textAlign=n.textAlign||"left",t._textBaseline=n.textBaseline||"middle",t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"textAlign",get:function(){return this._textAlign},set:function(t){this._textAlign=t,this.doChangeStyle()}},{key:"textBaseline",get:function(){return this._textBaseline},set:function(t){this._textBaseline=t,this.doChangeStyle()}}]),e}(n(24));t.exports=o},function(t,e,n){var r,o=n(14),i={CHANGE_STYLE:"change_style"},l=function(t){function e(){var t,n=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).bgColor;return h(this,e),(t=a(this,s(e).call(this)))._bgColor=n,t}return c(e,t),v(e,null,[{key:"EVENT_TYPE",get:function(){return i}},{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"doChangeStyle",value:function(){this.fireListeners(i.CHANGE_STYLE)}},{key:"clone",value:function(){return new e(this)}},{key:"bgColor",get:function(){return this._bgColor},set:function(t){this._bgColor=t,this.doChangeStyle()}}]),e}(o);t.exports=l},function(t,e,n){(function(e){var r=n(0).isDef,o=["content","font","color","className","isLiga","width","src","svg","name","path"],i={},a={};function l(t,e){var n=a[t]||(a[t]={});if(n[e])return n[e];var r=i[t]||(i[t]=document.createElement(t));r.className=e,document.body.appendChild(r);try{var o=document.defaultView.getComputedStyle(r,"::before"),l=o.getPropertyValue("content");l.length>=3&&('"'===l[0]||"'"===l[0])&&l[0]===l[l.length-1]&&(l=l.substr(1,l.length-2));var s=o.getPropertyValue("font");s||(s="".concat(o.getPropertyValue("font-style")," ").concat(o.getPropertyValue("font-variant")," ").concat(o.getPropertyValue("font-weight")," ").concat(o.getPropertyValue("font-size"),"/").concat(o.getPropertyValue("line-height")," ").concat(function(t){for(var e=[],n=t.split(/,\s*/),r=0;r<n.length;r++){var o=n[r].replace(/['"]/g,"");-1!==o.indexOf(" ")||/^\d/.test(o)?e.push("'".concat(o,"'")):e.push(o)}return e.join(",")}(o.getPropertyValue("font-family"))));var c=o.getPropertyValue("color"),u=r.clientWidth,f=(o.getPropertyValue("font-feature-settings")||"").indexOf("liga")>-1;return n[e]={content:l,font:s,color:c,width:u,isLiga:f}}finally{document.body.removeChild(r)}}function s(t){if(!t)return t;if(e(t))return t;var n={},r=0;o.forEach((function(n){t[n]&&(r=e(t[n])?Math.max(r,t[n].length):Math.max(r,1))})),o.forEach((function(o){n[o]=function(t,n){var r=[];if(e(t)){r.push.apply(r,f(t));for(var o=t.length;o<n;o++)r.push(null)}else for(var i=0;i<n;i++)r.push(t);return r}(t[o],r)}));for(var i=[],a=function(t){var e={};o.forEach((function(r){e[r]=n[r][t]})),i.push(e)},l=0;l<r;l++)a(l);return i}t.exports={toNormarizeArray:function(t){var e=s(t);return e?e.map((function(t){return function(t){var e={};for(var n in t)"className"!==n&&(e[n]=t[n]);if(t.className){var o=l(t.tagName||"i",t.className);for(var i in o)r(t[i])||(e[i]=o[i])}return e}(t)})):e},get iconPropKeys(){return o},getIconProps:l}}).call(this,n(2).Array_isArray)},function(t,e,n){var r=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,t),this._disabled=e.disabled}return v(t,[{key:"clone",value:function(){return new t(this)}},{key:"bindGridEvent",value:function(t,e){return[]}},{key:"onChangeDisabledInternal",value:function(){}},{key:"disabled",get:function(){return this._disabled},set:function(t){this._disabled=t,this.onChangeDisabledInternal()}}]),t}();t.exports=r},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._color=n.color,t._font=n.font,t._textOverflow=n.textOverflow||"ellipsis",t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"color",get:function(){return this._color},set:function(t){this._color=t,this.doChangeStyle()}},{key:"font",get:function(){return this._font},set:function(t){this._font=t,this.doChangeStyle()}},{key:"textOverflow",get:function(){return this._textOverflow},set:function(t){this._textOverflow=t,this.doChangeStyle()}}]),e}(n(107));t.exports=o},function(t,e,n){var r=n(0),o=r.extend,i=r.getIgnoreCase,a=n(112),l=a.Theme,s=n(57),c=new l(n(113)),u=new l(n(114)),f={BASIC:c,MATERIAL_DESIGN:u},h=u,d={get default(){return h},set default(t){h=d.of(t)||h},get MATERIAL_DESIGN(){return u},get BASIC(){return c},theme:a,of:function(t){if(!t)return null;if("string"==typeof t){var e=i(d.choices,t);return e||null}return t instanceof l?t:new l(t)},get choices(){return o(f,s)}};t.exports=d},function(t,e,r){var o=r(0),i=o.array,l=o.isDef,u=o.isPromise,d=o.getOrApply,g=o.applyChainSafe,p=o.emptyFn,y=r(59),_=r(14),m={UPDATE_LENGTH:"update_length",UPDATED_LENGTH:"updated_length",UPDATED_ORDER:"updated_order"};function w(t,e){return t=d(t),u(t)&&(t=t.then((function(t){return u(t)||e(t),w(t,e)})),e(t)),t}function C(t,e){if(!t._sortedIndexMap)return e;var n=t._sortedIndexMap[e];return l(n)?n:e}var x=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this)))._get=n.get&&n.get.bind(n)||void 0,t._length=n.length||0,t._sortedIndexMap=!1,t}return c(e,t),v(e,null,[{key:"ofArray",value:function(t){return new e({get:function(e){return t[e]},length:t.length})}},{key:"EVENT_TYPE",get:function(){return m}}]),v(e,[{key:"get",value:function(t){return this.getOriginal(C(this,t))}},{key:"getField",value:function(t,e){return this.getOriginalField(C(this,t),e)}},{key:"hasField",value:function(t,e){return this.hasOriginalField(C(this,t),e)}},{key:"setField",value:function(t,e,n){return this.setOriginalField(C(this,t),e,n)}},{key:"sort",value:function(t,e){var n=this,r=new Array(this._length),o="desc"!==e?function(t,e){return t===e?0:t>e?1:-1}:function(t,e){return t===e?0:t<e?1:-1};return y.sortPromise((function(t){return l(r[t])?r[t]:r[t]=t}),(function(t,e){r[t]=e}),this._length,o,(function(e){return n.getOriginalField(e,t)})).then((function(){n._sortedIndexMap=r,n.fireListeners(m.UPDATED_ORDER)}))}},{key:"dispose",value:function(){n(s(e.prototype),"dispose",this).call(this)}},{key:"getOriginal",value:function(t){var e=this;return w(this._get(t),(function(n){e.recordPromiseCallBackInternal(t,n)}))}},{key:"getOriginalField",value:function(t,e){var n=this;if(l(e))return function t(e,n,r){if(l(e)){if(u(e))return e.then((function(e){return t(e,n,r)}));if(n in e)return w(e[n],r);if("function"==typeof n)return w(n(e),r);var o="".concat(n).split(".");return o.length<=1?w(e[n],r):w(g.apply(void 0,[e,function(e,n){return t(e,n,p)}].concat(f(o))),r)}}(this.getOriginal(t),e,(function(r){n.fieldPromiseCallBackInternal(t,e,r)}))}},{key:"hasOriginalField",value:function(t,e){if(!l(e))return!1;if("function"==typeof e)return!0;var n=this.getOriginal(t);return n&&e in n}},{key:"setOriginalField",value:function(t,e,n){if(l(e))return function(t,e,n){if(e in t)t[e]=n;else{if("function"==typeof e)return e(t,n);if("string"==typeof e)for(var r="".concat(e).split("."),o=t,i=r.length,a=0;a<i;a++){var l=r[a];a===i-1?o[l]=n:o=o[l]||(o[l]={})}else t[e]=n}return!0}(this.getOriginal(t),e,n)}},{key:"fieldPromiseCallBackInternal",value:function(t,e,n){}},{key:"recordPromiseCallBackInternal",value:function(t,e){}},{key:"length",get:function(){return this._length},set:function(t){if(this._length!==t){var e=this.fireListeners(m.UPDATE_LENGTH,t);i.findIndex(e,(function(t){return!t}))>=0||(this._length=t,this.fireListeners(m.UPDATED_LENGTH,this._length))}}}]),e}(_);x.EMPTY=new x({length:0}),t.exports=x},function(e,n,r){var o=r(0).browser;function i(){return t.Path2D&&!o.Edge?t.Path2D:r(124)}e.exports={fill:function(t,e,n,r,o,a){e.save();try{var l=t.width,s=t.height,c=t.ud,u=t.x,f=void 0===u?0:u,h=t.y,d=void 0===h?0:h,v=(o=o||l)/l,g=(a=a||s)/(c?-s:s);n=n||0,r=c?(r||0)+-s*g:r||0,e.translate(n,r),e.scale(v,g),0===f&&0===d||e.translate(f,d);var p=i();t.path2d=t.path2d||new p(t.d),e.fill(t.path2d)}finally{e.restore()}},get Path2D(){return i()}}},function(t,e,n){var r=(0,n(0).extend)(n(5).EVENT_TYPE,{CHANGED_VALUE:"changed_value",CHANGED_HEADER_VALUE:"changed_header_value"});t.exports=r},function(t,e,n){var r=function(){function t(e){h(this,t),this._grid=e}return v(t,[{key:"dispose",value:function(){this.detachMessageElement(),this._messageElement&&this._messageElement.dispose(),this._messageElement=null}},{key:"_getMessageElement",value:function(){return this._messageElement||(this._messageElement=this.createMessageElementInternal())}},{key:"createMessageElementInternal",value:function(){}},{key:"drawCellMessageInternal",value:function(t,e,n,r,o,i){}},{key:"attachMessageElement",value:function(t,e,n){this._getMessageElement().attach(this._grid,t,e,n)}},{key:"moveMessageElement",value:function(t,e){this._getMessageElement().move(this._grid,t,e)}},{key:"detachMessageElement",value:function(){this._getMessageElement()._detach()}},{key:"drawCellMessage",value:function(t,e,n,r,o,i){this.drawCellMessageInternal(t,e,n,r,o,i)}}]),t}();t.exports=r},function(t,e,n){var r=n(3),o=n(10).createElement,i="cheetah-grid__message-element",a="".concat(i,"__message"),l="".concat(i,"--hidden"),s="".concat(i,"--shown"),c=function(){function t(){h(this,t),this._handler=new r;var e=this._rootElement=function(){n(137);var t=o("div",{classList:[i,l]}),e=o("span",{classList:[a]});return t.appendChild(e),t}();this._messageElement=e.querySelector(".".concat(a))}return v(t,[{key:"dispose",value:function(){this.detach(),this._handler.dispose(),this._rootElement=null,this._messageElement=null}},{key:"attach",value:function(t,e,n,r){var o=this._rootElement,i=this._messageElement;o.classList.remove(s),o.classList.add(l),this._attachCell(t,e,n)?(o.classList.add(s),o.classList.remove(l),i.textContent=r.message):this._detach()}},{key:"move",value:function(t,e,n){var r=this._rootElement;this._attachCell(t,e,n)?(r.classList.add(s),r.classList.remove(l)):this._detach()}},{key:"detach",value:function(){this._detach()}},{key:"_detach",value:function(){var t=this._rootElement;t.parentElement&&(t.parentElement.removeChild(t),t.classList.remove(s),t.classList.add(l))}},{key:"_attachCell",value:function(t,e,n){var r=this._rootElement,o=t.getAttachCellArea(e,n),i=o.element,a=o.rect,l=a.bottom,s=a.left,c=a.width,u=t.frozenRowCount,f=t.frozenColCount;if(n>=u&&u>0){if(l<t.getAttachCellArea(e,u-1).rect.bottom)return!1}else if(l<0)return!1;if(e>=f&&f>0){if(s<t.getAttachCellArea(f-1,n).rect.right)return!1}else if(s<0)return!1;var h=i.offsetHeight,d=i.offsetWidth;return!(h<l||d<s||(r.style.top="".concat(l.toFixed(),"px"),r.style.left="".concat(s.toFixed(),"px"),r.style.width="".concat(c.toFixed(),"px"),r.parentElement!==i&&i.appendChild(r),0))}}]),t}();t.exports=c},function(t,e,n){var r=n(19);t.exports={drawExclamationMarkBox:function(t,e,n){var o=e.bgColor,i=e.color,a=t.getContext(),l=t.getRect();a.fillStyle=o;var s=l.copy();s.left=s.right-24,a.fillRect(s.left,s.top,s.width,s.height-1);var c=i,u=s.left+(s.width-4)/2,f=s.top+(s.height-20)/2;n.fillRectWithState(new r(u,f,4,12),t,{fillColor:c}),n.fillRectWithState(new r(u,f+16,4,4),t,{fillColor:c})},drawInfomationMarkBox:function(t,e,n){var o=e.bgColor,i=e.color,a=t.getContext(),l=t.getRect();a.fillStyle=o;var s=l.copy();s.left=s.right-24,a.fillRect(s.left,s.top,s.width,s.height-1);var c=i,u=s.left+(s.width-4)/2,f=s.top+(s.height-20)/2;n.fillRectWithState(new r(u,f,4,4),t,{fillColor:c}),n.fillRectWithState(new r(u,f+8,4,12),t,{fillColor:c})}}},function(t,e,n){var r,o=function(){n(71),r=function(){var t=document.createElement("div"),e=t.style;e.position="absolute",e.height="9999px",e.width="calc(100vw - 100%)",e.opacity=0,t.textContent="x",document.body.appendChild(t);var n=document.defaultView.getComputedStyle(t,"").width;return document.body.removeChild(t),parseInt(n,10)}()||10;var t=document.createElement("style");t.setAttribute("type","text/css"),t.setAttribute("data-name","cheetah-grid"),t.innerHTML="\n.cheetah-grid .grid-scroll-end-point {\n\twidth: ".concat(r,"px;\n\theight: ").concat(r,"px;\n}\n.cheetah-grid > canvas {\n\twidth: -webkit-calc(100% - ").concat(r,"px);\n\twidth: calc(100% - ").concat(r,"px);\n\theight: -webkit-calc(100% - ").concat(r,"px);\n\theight: calc(100% - ").concat(r,"px);\n}\n\t\t"),document.head.appendChild(t)},i={initDocument:function(){i.initDocument=function(){},o()},getScrollBarSize:function(){return r}};t.exports=i},function(t,e,n){var r=n(8),o=r.calcBasePosition,i=r.calcStartPosition,a=r.getFontSize,l=Math.ceil,s=Math.PI;function c(t,e,n,r,o,i){t.beginPath(),t.arc(e+i,n+i,i,-s,-.5*s,!1),t.arc(e+r-i,n+i,i,-.5*s,0,!1),t.arc(e+r-i,n+o-i,i,0,.5*s,!1),t.arc(e+i,n+o-i,i,.5*s,s,!1),t.closePath()}function u(t,e,n,r,o,i){c(t,e,n,r,o,i),t.fill()}function f(t,e,n,r,o,i){c(t,e,n,r,o,i),t.stroke()}function h(t){return{width:a(t,null).width}}var d={roundRect:c,fillRoundRect:u,strokeRoundRect:f,drawCheckbox:function(t,e,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},i=o.uncheckBgColor,a=void 0===i?"#FFF":i,s=o.checkBgColor,c=void 0===s?"rgb(76, 73, 72)":s,d=o.borderColor,v=void 0===d?"#000":d,g=o.boxSize,p=void 0===g?h(t).width:g,y="number"==typeof r?r>1?1:r:1;t.save();try{if(t.fillStyle=r?c:a,u(t,l(e)-1,l(n)-1,l(p+1),l(p+1),p/5),t.lineWidth=1,t.strokeStyle=v,f(t,l(e)-.5,l(n)-.5,l(p),l(p),p/5),r){t.lineWidth=l(p/10),t.strokeStyle=a;var _=p/4,m=p/2*.9,w=e+.2*p,C=n+p/2;y<.5&&(_*=2*y),t.beginPath(),t.moveTo(w,C),t.lineTo(w+_,C+_),y>.5&&(y<1&&(m*=2*(y-.5)),t.lineTo(w+_+m,C+_-m)),t.stroke()}}finally{t.restore()}},measureCheckbox:h,fillTextRect:function(t,e,n,r,i,a){var l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:{},s=l.offset,c=void 0===s?2:s,u=l.padding,f={left:n,top:r,width:i,height:a,right:n+i,bottom:r+a};t.save();try{t.beginPath(),t.rect(f.left,f.top,f.width,f.height),t.clip();var h=o(t,f,{offset:c,padding:u});t.fillText(e,h.x,h.y)}finally{t.restore()}},drawButton:function(t,e,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},a=i.backgroundColor,s=void 0===a?"#FFF":a,c=i.bgColor,f=void 0===c?s:c,h=i.radius,d=void 0===h?4:h,v=i.shadow,g=void 0===v?{}:v;t.save();try{if(t.fillStyle=f,g){var p=g.color,y=void 0===p?"rgba(0, 0, 0, 0.24)":p,_=g.blur,m=void 0===_?1:_,w=g.offsetX,C=void 0===w?0:w,x=g.offsetY,b=void 0===x?2:x,k=g.offset,E=(k=void 0===k?{}:k).x,I=void 0===E?C:E,L=k.y,S=void 0===L?b:L;t.shadowColor=y,t.shadowBlur=m,t.shadowOffsetX=I,t.shadowOffsetY=S}u(t,l(e),l(n),l(r),l(o),d)}finally{t.restore()}},drawInlineImageRect:function(t,e,n,r,o,a,l,s,c,u,f,h){var d=arguments.length>12&&void 0!==arguments[12]?arguments[12]:{},v=d.offset,g=void 0===v?2:v,p=d.padding,y={left:c,top:u,width:f,height:h,right:c+f,bottom:u+h};t.save();try{t.beginPath(),t.rect(y.left,y.top,y.width,y.height),t.clip();var _=i(t,y,l,s,{offset:g,padding:p});t.drawImage(e,n,r,o,a,_.x,_.y,l,s)}finally{t.restore()}},strokeColorsRect:function(t,e,n,r,o,i){e[0]===e[1]&&e[0]===e[2]&&e[0]===e[3]?e[0]&&(t.strokeStyle=e[0],t.strokeRect(n,r,o,i)):function(n){for(var r=0;r<e.length;r++){var o=e[r],i=e[r-1];if(o){if(i!==o){i&&(t.strokeStyle=i,t.stroke());var a=n[r];t.beginPath(),t.moveTo(a.x,a.y)}var l=n[r+1];t.lineTo(l.x,l.y)}else i&&(t.strokeStyle=i,t.stroke())}var s=e[e.length-1];s&&(t.strokeStyle=s,t.stroke())}([{x:n,y:r},{x:n+o,y:r},{x:n+o,y:r+i},{x:n,y:r+i},{x:n,y:r}])}};t.exports=d},function(t,e,n){var r=n(77),o=n(92),i=n(42);t.exports={action:r,type:o,style:i}},function(t,e,n){var r=n(39),o=r.bindCellClickAction,i=r.bindCellKeyAction,l=n(15).isDisabledRecord,u=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._action=n.action,t}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"getState",value:function(t){return{}}},{key:"bindGridEvent",value:function(t,e,n){var r=this,a=this.getState(t),s=function(e){if(!l(r.disabled,t,e.row)){var n=t.getRowRecord(e.row);r._action(n)}};return[].concat(f(o(t,e,n,{action:s,mouseOver:function(e){return!l(r.disabled,t,e.row)&&(a.mouseActiveCell={col:e.col,row:e.row},t.invalidateCell(e.col,e.row),!0)},mouseOut:function(e){delete a.mouseActiveCell,t.invalidateCell(e.col,e.row)}})),f(i(t,e,n,{action:s})))}},{key:"action",get:function(){return this._action},set:function(t){this._action=t}}]),e}(n(21));t.exports=u},function(t,e,n){var r=n(0),o=r.isPromise,i=r.event.cancel,a=n(5).EVENT_TYPE,l=a.CLICK_CELL,s=a.MOUSEOVER_CELL,c=a.MOUSEOUT_CELL,u=a.KEYDOWN,h=13;t.exports={bindCellClickAction:function(t,e,n,r){var i=r.action,a=r.mouseOver,u=r.mouseOut;return[t.listen(l,(function(e){n.isTarget(e.col,e.row)&&(o(t.getRowRecord(e.row))||i({col:e.col,row:e.row}))})),t.listen(s,(function(e){n.isTarget(e.col,e.row)&&(o(t.getRowRecord(e.row))||a&&!a({col:e.col,row:e.row})||(t.getElement().style.cursor="pointer"))})),t.listen(c,(function(e){n.isTarget(e.col,e.row)&&(u&&u({col:e.col,row:e.row}),t.getElement().style.cursor="")}))]},bindCellKeyAction:function(t,e,n,r){var a=r.action,l=r.acceptKeys,s=void 0===l?[]:l;return s=[].concat(f(s),[h]),[t.listen(u,(function(e,r){if(-1!==s.indexOf(e)){var l=t.selection.select;n.isTarget(l.col,l.row)&&(o(t.getRowRecord(l.row))||(a({col:l.col,row:l.row}),i(r)))}}))]}}},function(t,e,n){var r=n(0).event.cancel,o=n(15),i=o.isDisabledRecord,l=o.isReadOnlyRecord,u=n(16),f=n(5).EVENT_TYPE,d=f.INPUT_CELL,g=f.PASTE_CELL,p=f.EDITABLEINPUT_CELL,y=f.SELECTED_CELL,_=f.DBLCLICK_CELL,m=f.DBLTAP_CELL,w=f.KEYDOWN,C=f.MODIFY_STATUS_EDITABLEINPUT_CELL,x=f.SCROLL,b=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),a(this,s(e).call(this,t))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"onInputCellInternal",value:function(t,e,n){throw new Error}},{key:"onOpenCellInternal",value:function(t,e){throw new Error}},{key:"onChangeSelectCellInternal",value:function(t,e,n){throw new Error}},{key:"onSetInputAttrsInternal",value:function(t,e,n){throw new Error}},{key:"onGridScrollInternal",value:function(t){throw new Error}},{key:"bindGridEvent",value:function(t,e,n){var o=this,a=function(e){l(o.readOnly,t,e.row)||i(o.disabled,t,e.row)||o.onOpenCellInternal(t,e)},s=function(e,n){l(o.readOnly,t,e.row)||i(o.disabled,t,e.row)||o.onInputCellInternal(t,e,n)};return[t.listen(d,(function(t){n.isTarget(t.col,t.row)&&s({col:t.col,row:t.row},t.value)})),t.listen(g,(function(t){t.multi||n.isTarget(t.col,t.row)&&(r(t.event),s({col:t.col,row:t.row},t.normalizeValue))})),t.listen(_,(function(t){n.isTarget(t.col,t.row)&&a({col:t.col,row:t.row})})),t.listen(m,(function(t){n.isTarget(t.col,t.row)&&(a({col:t.col,row:t.row}),r(t.event))})),t.listen(w,(function(e,r){if(113===e||13===e){var o=t.selection.select;n.isTarget(o.col,o.row)&&a({col:o.col,row:o.row})}})),t.listen(y,(function(e){o.onChangeSelectCellInternal(t,{col:e.col,row:e.row},e.selected)})),t.listen(x,(function(){o.onGridScrollInternal(t)})),t.listen(p,(function(e){return!!n.isTarget(e.col,e.row)&&!l(o.readOnly,t,e.row)&&!i(o.disabled,t,e.row)})),t.listen(C,(function(e){n.isTarget(e.col,e.row)&&(l(o.readOnly,t,e.row)||i(o.disabled,t,e.row)||o.onSetInputAttrsInternal(t,{col:e.col,row:e.row},e.input))}))]}}]),e}(u);t.exports=b},function(t,e,n){(function(e){t.exports={normalize:function t(n){if(!n)return[];if(e(n))return n;if("string"==typeof n)return t(JSON.parse(n));var r=[];for(var o in n)r.push({value:o,caption:n[o]});return r}}}).call(this,n(2).Array_isArray)},function(t,e,n){var r=n(24),o=n(4),i=n(43),a=n(44),l=n(45),s=n(46),c=n(47),u=n(48),f=n(49),h=n(50),d={get EVENT_TYPE(){return r.EVENT_TYPE},get BaseStyle(){return r},get Style(){return o},get NumberStyle(){return i},get CheckStyle(){return a},get ButtonStyle(){return l},get ImageStyle(){return s},get IconStyle(){return c},get PercentCompleteBarStyle(){return u},get MultilineTextStyle(){return f},get MenuStyle(){return h},of:function(t,e,n){return t?t instanceof o?t:"function"==typeof t?d.of(t(e),e,n):e&&t in e?d.of(e[t],e,n):new n(t):n.DEFAULT}};t.exports=d},function(t,e,n){var r,o=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"right":e;return t.textAlign=n,t}(t)))}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}}]),e}(n(4));t.exports=o},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,e),t=a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"center":e;return t.textAlign=n,t}(n)));var r=n.uncheckBgColor,o=n.checkBgColor,i=n.borderColor;return t._uncheckBgColor=r,t._checkBgColor=o,t._borderColor=i,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"uncheckBgColor",get:function(){return this._uncheckBgColor},set:function(t){this._uncheckBgColor=t,this.doChangeStyle()}},{key:"checkBgColor",get:function(){return this._checkBgColor},set:function(t){this._checkBgColor=t,this.doChangeStyle()}},{key:"borderColor",get:function(){return this._borderColor},set:function(t){this._borderColor=t,this.doChangeStyle()}}]),e}(n(23));t.exports=o},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,e),t=a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"center":e;return t.textAlign=n,t}(n)));var r=n.buttonBgColor;return t._buttonBgColor=r,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"buttonBgColor",get:function(){return this._buttonBgColor},set:function(t){this._buttonBgColor=t,this.doChangeStyle()}}]),e}(n(4));t.exports=o},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"center":e;return t.textAlign=n,t}(n))))._imageSizing=n.imageSizing,t._margin=n.margin||4,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"imageSizing",get:function(){return this._imageSizing},set:function(t){this._imageSizing=t,this.doChangeStyle()}},{key:"margin",get:function(){return this._margin},set:function(t){this._margin=t,this.doChangeStyle()}}]),e}(n(23));t.exports=o},function(t,e,n){var r,o=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"center":e;return t.textAlign=n,t}(t)))}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}}]),e}(n(4));t.exports=o},function(t,e,n){var r,o=n(4),i=function(t){return t>80?"#20a8d8":t>50?"#4dbd74":t>20?"#ffc107":"#f86c6b"},l=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._barColor=n.barColor||i,t._barBgColor=n.barBgColor||"#f0f3f5",t._barHeight=n.barHeight||3,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"barColor",get:function(){return this._barColor},set:function(t){this._barColor=t,this.doChangeStyle()}},{key:"barBgColor",get:function(){return this._barBgColor},set:function(t){this._barBgColor=t,this.doChangeStyle()}},{key:"barHeight",get:function(){return this._barHeight},set:function(t){this._barHeight=t,this.doChangeStyle()}}]),e}(o);t.exports=l},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,function(t){var e=t.textBaseline,n=void 0===e?"top":e;return t.textBaseline=n,t}(n))))._lineHeight=n.lineHeight||"1em",t._autoWrapText=n.autoWrapText||!1,t._lineClamp=n.lineClamp,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"lineHeight",get:function(){return this._lineHeight},set:function(t){this._lineHeight=t,this.doChangeStyle()}},{key:"lineClamp",get:function(){return this._lineClamp},set:function(t){this._lineClamp=t,this.doChangeStyle()}},{key:"autoWrapText",get:function(){return this._autoWrapText},set:function(t){this._autoWrapText=t,this.doChangeStyle()}}]),e}(n(4));t.exports=o},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,e),t=a(this,s(e).call(this,n));var r=n.appearance;return t._appearance=r,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"appearance",get:function(){return this._appearance||"menulist-button"},set:function(t){this._appearance=t,this.doChangeStyle()}}]),e}(n(4));t.exports=o},function(e,n,r){var o=r(0).then,i=r(97),a={};function l(e){if(!t.Promise)return console.error("Promise is not loaded. load Promise before this process."),{then:function(){return this}};var n=new Image,r=new t.Promise((function(t){n.onload=function(){t(n)}}));return n.onerror=function(t){var n=e.length>200?"".concat(e.substr(0,200),"..."):e;throw console.warn("cannot load: ".concat(n)),new Error("IMAGE LOAD ERROR: ".concat(n))},n.src=-1!==e.search(/^(data:)/)?e:"".concat(e,"?").concat(Date.now()),r}e.exports={loadImage:l,getCacheOrLoad:function(t,e,n){return function(t,e){return o(e,(function(e){var n=t.get(e);if(n)return n;var r=l(e).then((function(n){return t.put(e,n),n}));return t.put(e,r),r}))}(a[t]||(a[t]=new i(e)),n)}}},function(t,e,n){var r=n(26),o=n(105),i=n(106),l=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"disabled",get:function(){return this._disabled}}]),e}(o),u=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"disabled",get:function(){return this._disabled}}]),e}(i),f={ACTIONS:{SORT:new l,CHECK:new u},get BaseAction(){return r},get SortHeaderAction(){return o},get CheckHeaderAction(){return i},of:function(t){return t?"string"==typeof t?f.ACTIONS[t.toUpperCase()]||f.of(null):t:void 0},ofCell:function(t){return t.sort?"function"==typeof t.sort?new l({sort:function(e){var n=e.order,r=e.col,o=e.grid;return t.sort(n,r,o)}}):f.ACTIONS.SORT:f.of(t.action)}};t.exports=f},function(t,e,n){var r=n(0).event.cancel,o=n(5).EVENT_TYPE,i=o.CLICK_CELL,a=o.MOUSEOVER_CELL,l=o.MOUSEOUT_CELL,s=o.MOUSEMOVE_CELL,c=o.KEYDOWN,u=13;t.exports={bindCellClickAction:function(t,e,n){var r,o=n.action,c=n.mouseOver,u=n.mouseOut;return[t.listen(i,(function(t){e.isCellInRange(t.col,t.row)&&o({col:t.col,row:t.row})})),t.listen(a,(function(n){e.isCellInRange(n.col,n.row)&&(c&&!c({col:n.col,row:n.row})||(t.getElement().style.cursor="pointer",r=!0))})),t.listen(s,(function(n){e.isCellInRange(n.col,n.row)&&r&&!t.getElement().style.cursor&&(t.getElement().style.cursor="pointer")})),t.listen(l,(function(n){e.isCellInRange(n.col,n.row)&&(u&&u({col:n.col,row:n.row}),t.getElement().style.cursor="",r=!1)}))]},bindCellKeyAction:function(t,e,n){var o=n.action,i=n.acceptKeys,a=void 0===i?[]:i;return a=[].concat(f(a),[u]),[t.listen(c,(function(n,i){if(-1!==a.indexOf(n)){var l=t.selection.select;e.isCellInRange(l.col,l.row)&&(o({col:l.col,row:l.row}),r(i))}}))]}}},function(t,e,n){var r=n(18),o=n(109),i=n(110),a=n(111),l={TYPES:{DEFAULT:new o,SORT:new i,CHECK:new a},get BaseHeader(){return r},get Header(){return o},get SortHeader(){return o},get CheckHeader(){return a},of:function(t){return t?"string"==typeof t?l.TYPES[t.toUpperCase()]||l.of(null):t:l.TYPES.DEFAULT},ofCell:function(t){return t.sort?l.TYPES.SORT:l.of(t.headerType)}};t.exports=l},function(t,e,n){var r,o=n(14),i={CHANGE_STYLE:"change_style"},l=function(t){function e(){var t,n=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).bgColor;return h(this,e),(t=a(this,s(e).call(this)))._bgColor=n,t}return c(e,t),v(e,null,[{key:"EVENT_TYPE",get:function(){return i}},{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"doChangeStyle",value:function(){this.fireListeners(i.CHANGE_STYLE)}},{key:"clone",value:function(){return new e(this)}},{key:"bgColor",get:function(){return this._bgColor},set:function(t){this._bgColor=t,this.doChangeStyle()}}]),e}(o);t.exports=l},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,e),t=a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"center":e;return t.textAlign=n,t}(n)));var r=n.uncheckBgColor,o=n.checkBgColor,i=n.borderColor;return t._uncheckBgColor=r,t._checkBgColor=o,t._borderColor=i,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"uncheckBgColor",get:function(){return this._uncheckBgColor},set:function(t){this._uncheckBgColor=t,this.doChangeStyle()}},{key:"checkBgColor",get:function(){return this._checkBgColor},set:function(t){this._checkBgColor=t,this.doChangeStyle()}},{key:"borderColor",get:function(){return this._borderColor},set:function(t){this._borderColor=t,this.doChangeStyle()}}]),e}(n(27));t.exports=o},function(t,e,n){t.exports={}},function(t,e,n){var r=n(29),o=n(115),i=n(116);t.exports={DataSource:r,CachedDataSource:o,FilterDataSource:i}},function(e,n,r){var o=r(0).isPromise;function i(t){return t.then((function(t){return o(t)?i(t):t}))}function a(t,e,n){return new Promise((function(r){for(var a=[],l=new Array(n),s=function(e){var n=t(e),r={v:n,f:n};l[e]=r,o(n)&&a.push(i(n).then((function(t){r.v=t,r.f=t})))},c=0;c<n;c++)s(c);Promise.all(a).then((function(){return function(t,e){return e?new Promise((function(n){for(var r=t.length,a=[],l=function(n){var r=t[n];r.f=e(r.v),o(r.f)&&a.push(i(r.f).then((function(t){r.f=t})))},s=0;s<r;s++)l(s);Promise.all(a).then((function(){return n(t)}))})):Promise.resolve(t)}(l,e)})).then(r)}))}var l={sort:function(t,e,n,r,o){var i=function(t,e){for(var n=new Array(e),r=0;r<e;r++)n[r]=t(r);return n}(t,n);o?i.sort((function(t,e){return r(o(t),o(e))})):i.sort(r);for(var a=0;a<n;a++)e(a,i[a])},sortArray:function(t,e){Array.prototype.sort.call(t,e)},sortPromise:function(e,n,r,o,i){if(t.Promise)return a(e,i,r).then((function(t){t.sort((function(t,e){return o(t.f,e.f)}));for(var e=0;e<r;e++)n(e,t[e].v)}));l.sort(e,n,r,o,i);var s={then:function(t){return t(),s},catch:function(){return s}};return s}};e.exports=l},function(t,e,n){(function(e){var r=n(8),o=r.calcStartPosition,i=r.getFontSize,a=n(118),l=n(36),s=n(28),c=n(66).colorToRGB,u=n(19),d=n(0),g=d.getChainSafe,p=d.getOrApply,y=d.style.toBoxArray,_=d.isDef,m=n(61),w=n(20),C=n(63),x=a.of("…");function b(t,e,n,r,o){return p(t,{col:e,row:n,grid:r,context:o})}function k(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];var o,i=g.apply(void 0,[t.theme].concat(n));return _(i)?"function"!=typeof i?i:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var a=i.apply(void 0,e);return _(a)?a:(o=o||g.apply(void 0,[s.default].concat(n)),p.apply(void 0,[o].concat(e)))}:g.apply(void 0,[s.default].concat(n))}function E(t,e,n,r,o,i,a,s,c){function u(e,u,f){if(e.canDraw()){t.save();try{t.fillStyle=b(e.color()||t.fillStyle,a,s,c,t),t.font=e.font()||t.font,e.draw({ctx:t,canvashelper:l,rect:n,offset:r,offsetLeft:u,offsetRight:f,offsetTop:o,offsetBottom:i})}finally{t.restore()}}else e.onReady((function(){return c.invalidateCell(a,s)}))}if(1===e.length)u(e[0],0,0);else{var f=e.map((function(e){return(e.width({ctx:t})||0)-0})),h=f.reduce((function(t,e){return t+e})),d=0;e.forEach((function(t,e){var n=f[e];u(t,d,h-=n),d+=n}))}}function I(t,e){return a.buildInlines(t,e||"")}function L(t){return a.string(t)}function S(t){return T(t)&&"ellipsis"!==t&&1===(t=t.trim()).length?a.of(t[0]):x}function T(t){return t&&"clip"!==t&&"string"==typeof t}function A(t,e,n){for(var r=n-3,o=0,i=0;i<e.length;i++){var a=(e[i].width({ctx:t})||0)-0;if(o+a>r)return{index:i,lineWidth:o,remWidth:r-o};o+=a}return null}function R(t,e,n){var r=A(t,e,n);if(!r)return{beforeInlines:e,overflow:!1,afterInlines:[]};var o=r.index,i=r.remWidth,a=e[o],l=e.slice(0,o),s=[];if(a.canBreak()){var c=a.breakWord(t,i),u=c.before,h=c.after;if(!u&&!l.length){var d=a.breakAll(t,i);u=d.before,h=d.after}if(!u&&!l.length){var v=a.splitIndex(1);u=v.before,h=v.after}u&&l.push(u),h&&s.push(h),s.push.apply(s,f(e.slice(o+1)))}else l.length||l.push(a),s.push.apply(s,f(e.slice(l.length)));return{beforeInlines:l,overflow:!0,afterInlines:s}}function O(t,e,n,r){var o=A(t,e,n);if(!o)return{inlines:e,overflow:!1};var i=o.index,a=o.lineWidth,l=e[i],s=S(r),c=n-a-s.width({ctx:t}),u=e.slice(0,i);if(l.canBreak()){var f=l.breakAll(t,c).before;f&&u.push(f)}return u.push(s),{inlines:u,overflow:!0}}function B(t,e,n,r,o,i){var a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:{},l=a.offset,s=a.color,c=a.textAlign,u=a.textBaseline,f=a.font,h=a.textOverflow,d=a.icons;e.fillStyle=b(s,o,i,t,e),e.textAlign=c,e.textBaseline=u,e.font=f||e.font;var v=I(d,n);if(T(h)&&function(t,e,n){return!!A(t,e,n)}(e,v,r.width)){var g=O(e,v,r.width,h),p=g.inlines,y=g.overflow;v=p,t.setCellOverflowText(o,i,y&&L(n))}else t.setCellOverflowText(o,i,!1);E(e,v,r,l,0,0,o,i,t)}function M(t,e,n,r,o,a){var l,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:{},c=s.offset,u=s.color,f=s.textAlign,h=s.textBaseline,d=s.font,v=s.lineHeight,g=s.autoWrapText,p=s.lineClamp,y=s.textOverflow,_=s.icons;if(e.fillStyle=b(u,o,a,t,e),e.textAlign=f,e.textBaseline=h,e.font=d||e.font,"auto"===p){var m=r.height-2*c-2;p=Math.max(Math.floor(m/v),1)}if(g||p>0||T(y)){var w=r.width;l=[];var C=p>0?function(r,i){if(l.length+1>=p){if(0===r.length&&i)l.push([S(y)]),t.setCellOverflowText(o,a,n.map(L).join("\n"));else{var s=O(e,r,w,y),c=s.inlines,u=s.overflow;l.push(i&&!u?c.concat([S(y)]):c),(u||i)&&t.setCellOverflowText(o,a,n.map(L).join("\n"))}return!1}return!0}:function(){return!0},x=g?function(t,n){if(!C(t,n))return!1;for(;t.length;){if(!C(t,n))return!1;var r=R(e,t,w),o=r.beforeInlines,i=r.afterInlines;l.push(o),t=i}return!0}:T(y)?function(r,i){if(!C(r,i))return!1;var s=O(e,r,w,y),c=s.inlines,u=s.overflow;return l.push(c),u&&t.setCellOverflowText(o,a,n.map(L).join("\n")),!0}:function(t,e){return!!C(t,e)&&(l.push(t),!0)};t.setCellOverflowText(o,a,!1);for(var k=0;k<n.length;k++){var A=n[k];if(!x(I(0===k?_:void 0,A),k+1<n.length))break}}else t.setCellOverflowText(o,a,!1),l=n.map((function(t,e){return I(0===e?_:void 0,t)}));var B=0,M=v*(l.length-1);if("top"===e.textBaseline||"hanging"===e.textBaseline){var D=i(e,e.font).height,P=(v-D)/2;B+=P,M-=P}else if("bottom"===e.textBaseline||"alphabetic"===e.textBaseline||"ideographic"===e.textBaseline){var F=i(e,e.font).height,z=(v-F)/2;B-=z,M+=z}l.forEach((function(n){E(e,n,r,c,B,M,o,a,t),B+=v,M-=v}))}function D(t,e,n,r,i,a){var s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:{},u=s.animElapsedTime,f=void 0===u?1:u,h=s.uncheckBgColor,d=void 0===h?a.theme.checkbox.uncheckBgColor:h,v=s.checkBgColor,g=void 0===v?a.theme.checkbox.checkBgColor:v,p=s.borderColor,y=void 0===p?a.theme.checkbox.borderColor:p,_=s.textAlign,m=void 0===_?"center":_,w=s.textBaseline,C=void 0===w?"middle":w,x=arguments.length>7&&void 0!==arguments[7]?arguments[7]:{},b=l.measureCheckbox(t).width;t.textAlign=m,t.textBaseline=C;var k=o(t,e,b+1,b+1,x);if(d=a.getColor(d,n,r,t),g=a.getColor(g,n,r,t),y=a.getColor(y,n,r,t),0<f&&f<1){var E=c(d),I=c(g),L=function(t){var e=E[t],n=I[t];if(f>=1)return n;var r=e-n;return Math.ceil(e-r*f)},S=function(t){var e=E[t],n=I[t];if(f>=1)return e;var r=e-n;return Math.ceil(n+r*f)};d=i?d:"rgb(".concat(S("r")," , ").concat(S("g"),", ").concat(S("b"),")"),g="rgb(".concat(L("r")," , ").concat(L("g"),", ").concat(L("b"),")")}l.drawCheckbox(t,k.x,k.y,!!i&&f,{uncheckBgColor:d,checkBgColor:g,borderColor:y})}var P=function(){function t(e){h(this,t),this._grid=e}return v(t,[{key:"getThemeColor",value:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return k.apply(void 0,[this._grid].concat(e))}},{key:"font",get:function(){return k(this._grid,"font")}},{key:"underlayBackgroundColor",get:function(){return k(this._grid,"underlayBackgroundColor")}},{key:"color",get:function(){return k(this._grid,"color")}},{key:"frozenRowsColor",get:function(){return k(this._grid,"frozenRowsColor")}},{key:"defaultBgColor",get:function(){return k(this._grid,"defaultBgColor")}},{key:"frozenRowsBgColor",get:function(){return k(this._grid,"frozenRowsBgColor")}},{key:"selectionBgColor",get:function(){return k(this._grid,"selectionBgColor")}},{key:"borderColor",get:function(){return k(this._grid,"borderColor")}},{key:"frozenRowsBorderColor",get:function(){return k(this._grid,"frozenRowsBorderColor")}},{key:"highlightBorderColor",get:function(){return k(this._grid,"highlightBorderColor")}},{key:"checkbox",get:function(){var t=this._grid;return this._checkbox||(this._checkbox={get uncheckBgColor(){return k(t,"checkbox","uncheckBgColor")},get checkBgColor(){return k(t,"checkbox","checkBgColor")},get borderColor(){return k(t,"checkbox","borderColor")}})}},{key:"button",get:function(){var t=this._grid;return this._button||(this._button={get color(){return k(t,"button","color")},get bgColor(){return k(t,"button","bgColor")}})}},{key:"header",get:function(){var t=this._grid;return this._header||(this._header={get sortArrowColor(){return k(t,"header","sortArrowColor")}})}}]),t}();function F(t,n,r,o,i,a){if(e(n)){var s=y(n);l.strokeColorsRect(t,s,r,o,i,a)}else n&&(t.strokeStyle=n,t.strokeRect(r,o,i,a))}var z=function(){function t(e){h(this,t),this._grid=e,this._theme=new P(e)}return v(t,[{key:"createCalculator",value:function(t,e){return{calcWidth:function(n){return w.toPx(n,{get full(){return t.getRect().width},get em(){return i(t.getContext(),e).width}})},calcHeight:function(n){return w.toPx(n,{get full(){return t.getRect().height},get em(){return i(t.getContext(),e).height}})}}}},{key:"getColor",value:function(t,e,n,r){return b(t,e,n,this._grid,r)}},{key:"toBoxArray",value:function(t){return y(t)}},{key:"toBoxPixelArray",value:function(t,n,r){if("string"==typeof t||e(t)){var o=this.createCalculator(n,r),i=y(t);return[o.calcHeight(i[0]),o.calcWidth(i[1]),o.calcHeight(i[2]),o.calcWidth(i[3])]}return y(t)}},{key:"drawWithClip",value:function(t,e){var n=t.getDrawRect();if(n){var r=t.getContext();r.save();try{r.beginPath(),r.rect(n.left,n.top,n.width,n.height),r.clip(),e(r)}finally{r.restore()}}}},{key:"drawBorderWithClip",value:function(t,e){var n=t.getDrawRect();if(n){var r=t.getRect(),o=t.getContext();o.save();try{o.beginPath();var i=n.left,a=n.width;n.left===r.left&&(i+=-1,a+=1);var l=n.top,s=n.height;n.top===r.top&&(l+=-1,s+=1),o.rect(i,l,a,s),o.clip(),e(o)}finally{o.restore()}}}},{key:"text",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r.padding,i=r.offset,a=void 0===i?2:i,l=r.color,s=r.textAlign,c=void 0===s?"left":s,f=r.textBaseline,h=void 0===f?"middle":f,d=r.font,v=r.textOverflow,g=void 0===v?"clip":v,p=r.icons,y=e.getRect(),_=e.col,m=e.row;if(!l){l=this.theme.color;var w=this._grid.isFrozenCell(_,m);w&&w.row&&(l=this.theme.frozenRowsColor)}this.drawWithClip(e,(function(r){if(o){o=n.toBoxPixelArray(o,e,d);var i=y.left+o[3],s=y.top+o[0],f=y.width-o[1]-o[3],v=y.height-o[0]-o[2];y=new u(i,s,f,v)}B(n._grid,r,t,y,_,m,{offset:a,color:l,textAlign:c,textBaseline:h,font:d,textOverflow:g,icons:p})}))}},{key:"multilineText",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r.padding,i=r.offset,a=void 0===i?2:i,l=r.color,s=r.textAlign,c=void 0===s?"left":s,f=r.textBaseline,h=void 0===f?"middle":f,d=r.font,v=r.lineHeight,g=void 0===v?"1em":v,p=r.autoWrapText,y=void 0!==p&&p,_=r.lineClamp,m=void 0===_?0:_,w=r.textOverflow,C=void 0===w?"clip":w,x=r.icons,b=e.getRect(),k=e.col,E=e.row;if(!l){l=this.theme.color;var I=this._grid.isFrozenCell(k,E);I&&I.row&&(l=this.theme.frozenRowsColor)}this.drawWithClip(e,(function(r){if(o){o=n.toBoxPixelArray(o,e,d);var i=b.left+o[3],s=b.top+o[0],f=b.width-o[1]-o[3],v=b.height-o[0]-o[2];b=new u(i,s,f,v)}var p=n.createCalculator(e,d);g=p.calcHeight(g),M(n._grid,r,t,b,k,E,{offset:a,color:l,textAlign:c,textBaseline:h,font:d,lineHeight:g,autoWrapText:y,lineClamp:m,textOverflow:C,icons:x})}))}},{key:"fillText",value:function(t,e,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},i=o.color,a=o.textAlign,l=void 0===a?"left":a,s=o.textBaseline,c=void 0===s?"top":s,u=o.font,f=r.col,h=r.row;if(!i){i=this.theme.color;var d=this._grid.isFrozenCell(f,h);d&&d.row&&(i=this.theme.frozenRowsColor)}var v=r.getContext();v.save();try{v.fillStyle=b(i,f,h,this._grid,v),v.textAlign=l,v.textBaseline=c,v.font=u||v.font,v.fillText(t,e,n)}finally{v.restore()}}},{key:"fillCell",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.fillColor,o=void 0===r?this.theme.defaultBgColor:r,i=t.getRect();this.drawWithClip(t,(function(n){var r=t.col,a=t.row;n.fillStyle=b(o,r,a,e._grid,n),n.beginPath(),n.rect(i.left,i.top,i.width,i.height),n.fill()}))}},{key:"fillCellWithState",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.fillColor=this.getFillColorState(t,e),this.fillCell(t,e)}},{key:"fillRect",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.fillColor,o=void 0===r?this.theme.defaultBgColor:r,i=e.getContext();i.save();try{var a=e.col,l=e.row;i.fillStyle=b(o,a,l,this._grid,i),i.beginPath(),i.rect(t.left,t.top,t.width,t.height),i.fill()}finally{i.restore()}}},{key:"fillRectWithState",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n.fillColor=this.getFillColorState(e,n),this.fillRect(t,e,n)}},{key:"getFillColorState",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.getSelectState(),r=t.col,o=t.row;if(!n.selected&&n.selection)return this.theme.selectionBgColor;if(e.fillColor)return e.fillColor;var i=this._grid.isFrozenCell(r,o);return i&&i.row?this.theme.frozenRowsBgColor:this.theme.defaultBgColor}},{key:"border",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.borderColor,o=void 0===r?this.theme.borderColor:r,i=n.lineWidth,a=void 0===i?1:i,l=t.getRect();this.drawBorderWithClip(t,(function(n){var r=t.col,i=t.row,s=b(o,r,i,e._grid,n);if(1===a)n.lineWidth=1,F(n,s,l.left-.5,l.top-.5,l.width,l.height);else if(2===a)n.lineWidth=2,F(n,s,l.left,l.top,l.width-1,l.height-1);else{n.lineWidth=a;var c=a/2-1;F(n,s,l.left+c,l.top+c,l.width-a+1,l.height-a+1)}}))}},{key:"borderWithState",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.getRect(),o=t.getSelectState(),i=t.col,a=t.row;if(o.selected)n.borderColor=this.theme.highlightBorderColor,n.lineWidth=2,this.border(t,n);else{var l=this._grid.isFrozenCell(i,a);l&&l.row&&(n.borderColor=this.theme.frozenRowsBorderColor),n.lineWidth=1,this.border(t,n);var s=this._grid.selection.select;s.col+1===i&&s.row===a?this.drawBorderWithClip(t,(function(t){var n=y(b(e.theme.highlightBorderColor,s.col,s.row,e._grid,t));t.lineWidth=1,t.strokeStyle=n[1],t.beginPath(),t.moveTo(r.left-.5,r.top),t.lineTo(r.left-.5,r.bottom),t.stroke()})):s.col===i&&s.row+1===a&&this.drawBorderWithClip(t,(function(t){var n=y(b(e.theme.highlightBorderColor,s.col,s.row,e._grid,t));t.lineWidth=1,t.strokeStyle=n[0],t.beginPath(),t.moveTo(r.left,r.top-.5),t.lineTo(r.right,r.top-.5),t.stroke()}))}}},{key:"buildCheckBoxInline",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this,o=e.getContext(),i=l.measureCheckbox(o).width;return new C({draw:a,width:i+3,height:i+1,color:void 0});function a(o){var i=o.ctx,a=(o.canvashelper,o.rect),l=o.offset,s=o.offsetLeft,c=o.offsetRight,u=o.offsetTop,f=o.offsetBottom;D(i,a,e.col,e.row,t,r,n,{offset:l+1,padding:{left:s+1,right:c,top:u,bottom:f}})}}},{key:"checkbox",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.drawWithClip(e,(function(o){var i=e.col,a=e.row;D(o,e.getRect(),i,a,t,n,r)}))}},{key:"button",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r.bgColor,i=void 0===o?this.theme.button.bgColor:o,a=r.padding,s=r.offset,c=void 0===s?2:s,f=r.color,h=void 0===f?this.theme.button.color:f,d=r.textAlign,v=void 0===d?"center":d,g=r.textBaseline,p=void 0===g?"middle":g,y=r.shadow,_=r.font,m=r.textOverflow,w=void 0===m?"clip":m,C=r.icons,x=e.getRect();this.drawWithClip(e,(function(r){var o=e.col,s=e.row;a=n.toBoxPixelArray(a||x.height/8,e,_);var f=x.left+a[3],d=x.top+a[0],g=x.width-a[1]-a[3],m=x.height-a[0]-a[2];l.drawButton(r,f,d,g,m,{bgColor:i,radius:x.height/8,offset:c,shadow:y}),B(n._grid,r,t,new u(f,d,g,m),o,s,{offset:c,color:h,textAlign:v,textBaseline:p,font:_,textOverflow:w,icons:C})}))}},{key:"testFontLoad",value:function(t,e,n){return function(t,e,n,r){return!(t&&!m.check(t,e)&&(m.load(t,e,(function(){return function(t,e){var n=t.col,r=t.row;e.invalidateCell(n,r)}(n,r)})),1))}(t,e,n,this._grid)}},{key:"theme",get:function(){return this._theme}}]),t}();t.exports=z}).call(this,n(2).Array_isArray)},function(t,e,n){var r,o,i=n(0).isNode,a={};if(i)r=function(t,e,n){n()},o=function(){return!1};else{var l=!document.fonts;r=l?function(t,e,r){a["".concat(t," @ ").concat(e)]?r():n(120).load(t,e,(function(){a["".concat(t," @ ").concat(e)]=!0,r()}),(function(){a["".concat(t," @ ").concat(e)]=!0,r()}))}:function(t,e,n){a.all||a[t]?n():(document.fonts.ready.then((function(){a.all=!0})),document.fonts.load(t).then((function(){a[t]=!0,n()})))},o=l?function(t,e){return!!a["".concat(t," @ ").concat(e)]||(r(t,e,(function(){})),!1)}:function(t,e){return!(!a.all&&!a[t]&&!document.fonts.check(t)&&(r(t,e,(function(){})),1))}}t.exports={check:o,load:r}},function(t,e,n){var r=n(13),o=n(51).getCacheOrLoad,i=n(0).isPromise,l=function(t){function e(t){var n,r=t.src,o=t.width,l=t.height,c=t.imageLeft,u=t.imageTop,f=t.imageWidth,d=t.imageHeight;return h(this,e),(n=a(this,s(e).call(this)))._src=r,n._width=o,n._height=l,n._imageLeft=c,n._imageTop=u,n._imageWidth=f,n._imageHeight=d,n._onloaded=[],n._loaded=!1,i(r)?r.then((function(t){n._src=t,n._loadImage(t)})):n._loadImage(r),n}return c(e,t),v(e,[{key:"_loadImage",value:function(t){var e=this,n=this._inlineImg=o("InlineImage",50,t);i(n)?n.then((function(t){e._loaded=!0,e._inlineImg=t,e._onloaded.forEach((function(t){return t()}))})):this._loaded=!0}},{key:"width",value:function(t){return t.ctx,this._width||(this._loaded?this._inlineImg.width:0)}},{key:"font",value:function(){return null}},{key:"color",value:function(){return null}},{key:"canDraw",value:function(){return this._loaded}},{key:"onReady",value:function(t){(i(this._src)||i(this._inlineImg))&&this._onloaded.push((function(){return t()}))}},{key:"draw",value:function(t){var e=t.ctx,n=t.canvashelper,r=t.rect,o=t.offset,i=t.offsetLeft,a=t.offsetRight,l=t.offsetTop,s=t.offsetBottom,c=this._inlineImg;n.drawInlineImageRect(e,c,this._imageLeft||0,this._imageTop||0,this._imageWidth||c.width,this._imageHeight||c.height,this._width||c.width,this._height||c.height,r.left,r.top,r.width,r.height,{offset:o+1,padding:{left:i,right:a,top:l,bottom:s}})}},{key:"canBreak",value:function(){return!1}},{key:"toString",value:function(){return""}}]),e}(r);t.exports=l},function(t,e,n){var r=function(t){function e(t){var n,r=t.draw,o=t.width,i=t.height,l=t.color;return h(this,e),(n=a(this,s(e).call(this)))._draw=r,n._width=o,n._height=i,n._color=l,n}return c(e,t),v(e,[{key:"width",value:function(t){return t.ctx,this._width}},{key:"font",value:function(){return null}},{key:"color",value:function(){return this._color}},{key:"canDraw",value:function(){return!0}},{key:"onReady",value:function(t){}},{key:"draw",value:function(t){var e=t.ctx,n=t.canvashelper,r=t.rect,o=t.offset,i=t.offsetLeft,a=t.offsetRight,l=t.offsetTop,s=t.offsetBottom;this._draw({ctx:e,canvashelper:n,rect:r,offset:o,offsetLeft:i,offsetRight:a,offsetTop:l,offsetBottom:s})}},{key:"canBreak",value:function(){return!1}},{key:"toString",value:function(){return""}}]),e}(n(13));t.exports=r},function(t,e,n){var r=n(0).extend,o=n(65),i={get arrow_upward(){return n(127)},get arrow_downward(){return n(128)},get edit(){return n(129)},get add(){return n(130)},get star(){return n(131)},get star_border(){return n(132)},get star_half(){return n(133)}};t.exports={get:function(){return r(i,o)}}},function(t,e,n){t.exports={}},function(t,e,n){var r={};function o(t){return parseInt(t,16)}function i(t,e,n){return{r:t,g:e,b:n,a:arguments.length>3&&void 0!==arguments[3]?arguments[3]:1}}function a(t){var e=t.r,n=t.g,r=t.b,o=t.a;return 0<=e&&e<=255&&0<=n&&n<=255&&0<=r&&r<=255&&0<=o&&o<=1}function l(t){return Math.ceil(255*t/100)}function s(t){if(t.match(/^#[0-9a-f]{3}$/i))return n=(e=t)[1],r=e[2],s=e[3],i(o(n+n),o(r+r),o(s+s));var e,n,r,s,c,u,f,h,d,v,g;if(t.match(/^#[0-9a-f]{6}$/i))return u=(c=t)[1],f=c[2],h=c[3],d=c[4],v=c[5],g=c[6],i(o(u+f),o(h+d),o(v+g));var p=t.match(/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/i);if(p){var y=i(p[1]-0,p[2]-0,p[3]-0);if(a(y))return y}if(p=t.match(/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d(\.\d)?)\s*\)$/i)){var _=i(p[1]-0,p[2]-0,p[3]-0,p[4]-0);if(a(_))return _}if(p=t.match(/^rgb\(\s*(\d{1,3}(\.\d)?)%\s*,\s*(\d{1,3}(\.\d)?)%\s*,\s*(\d{1,3}(\.\d)?)%\s*\)$/i)){var m=i(l(p[1]),l(p[3]),l(p[5]));if(a(m))return m}if(p=t.match(/^rgba\(\s*(\d{1,3}(\.\d)?)%\s*,\s*(\d{1,3}(\.\d)?)%\s*,\s*(\d{1,3}(\.\d)?)%\s*,\s*(\d(\.\d)?)\s*\)$/i)){var w=i(l(p[1]),l(p[3]),l(p[5]),p[7]-0);if(a(w))return w}return null}t.exports={colorToRGB:function(t){return"string"!=typeof t?i(0,0,0,0):(t=t.toLowerCase().trim(),r[t]?r[t]:s(t)||(r[t]=function(t){var e=document.createElement("div"),n=e.style;n.color=t,n.position="fixed",n.height="1px",n.width="1px",n.opacity=0,document.body.appendChild(e);var r=document.defaultView.getComputedStyle(e,"").color;return document.body.removeChild(e),s(r)}(t)))}}},function(t,e,n){var r=n(68),o=n(76),i=n(37),a=n(104),l=n(28),s=n(58),c=n(117),u=n(60),f=n(64),h=n(152);t.exports={core:r,tools:o,ListGrid:c,columns:i,headers:a,themes:l,data:s,GridCanvasHelper:u,get icons(){return f.get()},register:h,get _internal(){return console.warn("use internal!!"),{color:n(66),sort:n(59),calc:n(20),symbolManager:n(1),path2DManager:n(30)}}}},function(t,e,n){var r=n(5);t.exports={DrawGrid:r}},function(e,n){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"===g(t)&&(r=t)}e.exports=r},function(t,e,n){var r=n(3),o=n(35),i=n(0).browser.heightLimit-1e3;function a(t){var e;if(t._height>i){var n=o.getScrollBarSize(),r=t._scrollable.offsetHeight,a=i-r+n,l=t._height-r+n;t._p=a/l,e=i}else t._p=1,e=t._height;t._endPointElement.style.top="".concat(e.toFixed(),"px"),t._endPointElement.style.left="".concat(t._width.toFixed(),"px")}var l=function(){function t(){h(this,t),this._handler=new r,this._scrollable=document.createElement("div"),this._scrollable.classList.add("grid-scrollable"),this._height=0,this._width=0,this._endPointElement=document.createElement("div"),this._endPointElement.classList.add("grid-scroll-end-point"),a(this),this._scrollable.appendChild(this._endPointElement)}return v(t,[{key:"calcTop",value:function(t){var e=t-this.scrollTop;return this._scrollable.scrollTop+e}},{key:"getElement",value:function(){return this._scrollable}},{key:"setScrollSize",value:function(t,e){this._width=t,this._height=e,a(this)}},{key:"onScroll",value:function(t){this._handler.on(this._scrollable,"scroll",t)}},{key:"dispose",value:function(){this._handler.dispose()}},{key:"scrollWidth",get:function(){return this._width},set:function(t){this._width=t,a(this)}},{key:"scrollHeight",get:function(){return this._height},set:function(t){this._height=t,a(this)}},{key:"scrollLeft",get:function(){return Math.ceil(this._scrollable.scrollLeft)},set:function(t){this._scrollable.scrollLeft=t}},{key:"scrollTop",get:function(){return Math.ceil(this._scrollable.scrollTop/this._p)},set:function(t){this._scrollable.scrollTop=t*this._p}}]),t}();t.exports=l},function(t,e,n){var r=n(72);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,"/**\n * core styles \n */\n.cheetah-grid .grid-scrollable {\n\theight: 100%;\n\twidth: 100%;\n\tposition: absolute;\n\toverflow: scroll;\n}\n.cheetah-grid .grid-scroll-end-point {\n\topacity: 0;\n\tposition: relative;\n}\n.cheetah-grid {\n\tposition: relative;\n\twidth: 100%;\n\theight: 100%;\n}\n.cheetah-grid > canvas {\n\tposition: absolute;\n\twidth: 0;\n\theight: 0;\n}\n.cheetah-grid .grid-focus-control {\n\tposition: relative !important;\n\twidth: 1px;\n\theight: 1px;\n\topacity: 0;\n\tpadding: 0;\n\tmargin: 0;\n\tbox-sizing: border-box;\n\tpointer-events: none;\n\tmax-width: 0;\n\tmax-height: 0;\n\tfloat: none !important;\n}\n.cheetah-grid input.grid-focus-control::-ms-clear {\n\tvisibility:hidden;\n}\n.cheetah-grid input.grid-focus-control.composition {\n\topacity: 1;\n\tmax-width: none;\n\tmax-height: none;\n}",""])},function(e,n){e.exports=function(e){var n=void 0!==t&&t.location;if(!n)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var r=n.protocol+"//"+n.host,o=r+n.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(t,e){var n,i=e.trim().replace(/^"(.*)"$/,(function(t,e){return e})).replace(/^'(.*)'$/,(function(t,e){return e}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(i)?t:(n=0===i.indexOf("//")?i:0===i.indexOf("/")?r+i:o+i.replace(/^\.\//,""),"url("+JSON.stringify(n)+")")}))}},function(t,e,n){var r=function(){function t(){h(this,t),this._keys=[],this._vals={},this._sorted=!1}return v(t,[{key:"put",value:function(t,e){t in this._vals||(this._keys.push(t),this._sorted=!1),this._vals[t]=e}},{key:"get",value:function(t){return this._vals[t]}},{key:"has",value:function(t){return null!=this._vals[t]}},{key:"each",value:function(t,e,n){var r=this._keys,o=r.length;this._sorted||(r.sort((function(t,e){return t<e?-1:t>e?1:0})),this._sorted=!0);for(var i=function(t,e){for(var n=0,r=t.length-1;n<=r;){var o=Math.floor((n+r)/2);if(t[o]===e)return o;t[o]>e?r=o-1:n=o+1}return r<0?0:r}(r,t);i<o;i++){var a=r[i];if(t<=a&&a<=e)n(this.get(a),a);else if(e<a)return}}}]),t}();t.exports=r},function(e,n,r){var o,i=r(0).isNode,a=new(r(3));function l(){i?o=1:(o=Math.ceil(t.devicePixelRatio||1))>1&&o%2!=0&&(o+=1)}l(),i||a.on(t,"resize",l),e.exports={transform:function(t){var e=t.getContext("2d"),n=t.getAttribute,r=t.setAttribute;t.getAttribute=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];var a=n.call.apply(n,[this,t].concat(r));return"width"!==t&&"height"!==t||(a/=o),a},t.setAttribute=function(t,n){var i="width"===t||"height"===t;i&&(n*=o);for(var a=arguments.length,l=new Array(a>2?a-2:0),s=2;s<a;s++)l[s-2]=arguments[s];var c=r.call.apply(r,[this,t,n].concat(l));return i&&e.scale(o,o),c},Object.defineProperty(t,"width",{get:function(){return t.getAttribute("width")},set:function(e){t.setAttribute("width",Math.floor(e))},configurable:!0,enumerable:!0}),Object.defineProperty(t,"height",{get:function(){return t.getAttribute("height")},set:function(e){t.setAttribute("height",Math.floor(e))},configurable:!0,enumerable:!0});var i=e.drawImage;return e.drawImage=function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];if(e!==t||1===o)return i.call.apply(i,[this,e].concat(r));this.save();try{return this.scale(1/o,1/o),r.length>4?(r[4]*=o,r[5]*=o):(r[0]*=o,r[1]*=o),i.call.apply(i,[this,e].concat(r))}finally{this.restore()}},t}}},function(t,e,n){var r=n(36);t.exports={canvashelper:r}},function(t,e,n){var r=n(21),o=n(38),i=n(16),l=n(78),u=n(79),f=n(80),d=n(84),g=n(88),p=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"disabled",get:function(){return this._disabled}},{key:"readOnly",get:function(){return this._readOnly}}]),e}(l),y=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"disabled",get:function(){return this._disabled}},{key:"readOnly",get:function(){return this._readOnly}}]),e}(f),_={ACTIONS:{CHECK:new p,INPUT:new y},get BaseAction(){return r},get Editor(){return i},get Action(){return o},get CheckEditor(){return l},get ButtonAction(){return u},get SmallDialogInputEditor(){return f},get InlineInputEditor(){return d},get InlineMenuEditor(){return g},of:function(t){return t?"string"==typeof t?_.ACTIONS[t.toUpperCase()]||_.of(null):t:void 0}};t.exports=_},function(t,e,n){var r=n(0),o=r.obj.setReadonly,i=r.isPromise,l=r.event.cancel,u=n(15),d=u.isDisabledRecord,g=u.isReadOnlyRecord,p=n(39),y=p.bindCellClickAction,_=p.bindCellKeyAction,m=n(22),w=n(16),C=n(1).CHECK_COLUMN_STATE_ID,x=n(5).EVENT_TYPE.PASTE_CELL;function b(t){if("number"==typeof t)return 0===t?1:0;if("string"==typeof t){if("false"===t)return"true";if("off"===t)return"on";if(t.match(/^0+$/))return t.replace(/^(0*)0$/,"$11");if("true"===t)return"false";if("on"===t)return"off";if(t.match(/^0*1$/))return t.replace(/^(0*)1$/,"$10")}return!t}var k=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"bindGridEvent",value:function(t,e,n){var r=this;t[C]||o(t,C,{});var a=t[C],s=function(e){var n="".concat(e.col,":").concat(e.row),o="".concat(n,"::block"),l="".concat(n,"::elapsed");if(!(g(r.readOnly,t,e.row)||d(r.disabled,t,e.row)||a[o])){var s=t.doChangeValue(e.col,e.row,b);if(s){var c=function(){m(200,(function(n){1===n?delete a[l]:a[l]=n,t.invalidateCell(e.col,e.row)}))};i(s)?(a[o]=!0,s.then((function(){delete a[o],c()}))):c()}}};return[].concat(f(y(t,e,n,{action:s,mouseOver:function(e){return!d(r.disabled,t,e.row)&&(a.mouseActiveCell={col:e.col,row:e.row},t.invalidateCell(e.col,e.row),!0)},mouseOut:function(e){delete a.mouseActiveCell,t.invalidateCell(e.col,e.row)}})),f(_(t,e,n,{action:function(e){for(var r=t.selection.range,o=t.selection.select.col,i=r.start.row;i<=r.end.row;i++)n.isTarget(o,i)&&s({col:o,row:i})},acceptKeys:[13,32]})),[t.listen(x,(function(e){if(!e.multi&&n.isTarget(e.col,e.row)){var r=e.normalizeValue.trim();t.doGetCellValue(e.col,e.row,(function(t){var n=b(t);"".concat(n).trim()===r&&(l(e.event),s({col:e.col,row:e.row}))}))}}))])}}]),e}(w);t.exports=k},function(t,e,n){var r=n(0).obj.setReadonly,o=n(38),i=n(1).BUTTON_COLUMN_STATE_ID,l=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"getState",value:function(t){return t[i]||r(t,i,{}),t[i]}}]),e}(o);t.exports=l},function(t,e,n){(function(e){var r=n(0).obj.setReadonly,o=n(81),i=n(1).SMALL_DIALOG_INPUT_EDITOR_STATE_ID,l=null,u=0;function f(t,e,n,a){t[i]||r(t,i,{}),l||(l=new o);var s=t[i];s.element||(s.element=l,u++,t.addDisposable({dispose:function(){--u||(l.dispose(),l=null)}})),l.attach(t,n,e.col,e.row,a)}function d(t){l&&l.detach(t)}var g=function(t){function n(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,n),(t=a(this,s(n).call(this,e)))._helperText=e.helperText,t._inputValidator=e.inputValidator,t._validator=e.validator,t._classList=e.classList,t._type=e.type,t}return c(n,t),v(n,[{key:"dispose",value:function(){this._inputDialogManager.dispose(),this._inputDialogManager=null}},{key:"clone",value:function(){return new n(this)}},{key:"onInputCellInternal",value:function(t,e,n){f(t,e,this,n)}},{key:"onOpenCellInternal",value:function(t,e){var n=this;t.doGetCellValue(e.col,e.row,(function(r){f(t,e,n,r)}))}},{key:"onChangeSelectCellInternal",value:function(t,e,n){d()}},{key:"onGridScrollInternal",value:function(t){d(!0)}},{key:"onChangeDisabledInternal",value:function(){d(!0)}},{key:"onChangeReadOnlyInternal",value:function(){d(!0)}},{key:"onSetInputAttrsInternal",value:function(t,e,n){o.setInputAttrs(this,t,n)}},{key:"classList",get:function(){return this._classList&&(e(this._classList)?this._classList:[this._classList])},set:function(t){this._classList=t}},{key:"type",get:function(){return this._type},set:function(t){this._type=t}},{key:"helperText",get:function(){return this._helperText}},{key:"inputValidator",get:function(){return this._inputValidator}},{key:"validator",get:function(){return this._validator}}]),n}(n(40));t.exports=g}).call(this,n(2).Array_isArray)},function(t,e,n){var r=n(0),o=r.event,i=o.getKeyCode,a=o.cancel,l=r.then,s=r.browser,c=n(3),u=n(10).createElement,d="cheetah-grid__small-dialog-input",g="".concat(d,"__input"),p="".concat(d,"--hidden"),y="".concat(d,"--shown");function _(t,e){e.tryWithOffEvents(t,"blur",(function(){!function(){t.focus();var e=t.value.length;try{if(void 0!==t.selectionStart)return t.selectionStart=e,void(t.selectionEnd=e)}catch(t){}if(document.selection){var n=t.createTextRange();n.collapse(),n.moveEnd("character",e),n.moveStart("character",e),n.select()}}()}))}function m(t,e,n){var r=t.type;n.type=r||""}var w=function(){function t(){h(this,t),this._handler=new c,this._dialog=function(){n(82);var t=u("div",{classList:[d,p]}),e=u("input",{classList:g});return e.readonly=!0,e.tabIndex=-1,t.appendChild(e),t}(),this._input=this._dialog.querySelector(".".concat(g)),this._bindDialogEvents()}return v(t,null,[{key:"setInputAttrs",value:function(){m.apply(void 0,arguments)}}]),v(t,[{key:"dispose",value:function(){var t=this._dialog;this.detach(),this._handler.dispose(),this._dialog=null,this._input=null,this._beforePropEditor=null,t.parentElement&&t.parentElement.removeChild(t)}},{key:"attach",value:function(t,e,n,r,o){var i=this,a=this._handler,l=this._dialog,c=this._input;this._beforePropEditor&&function(t,e,n,r){var o,i=r.classList;i&&(o=e.classList).remove.apply(o,f(i)),delete e.dataset.helperText,n.type=""}(0,l,c,this._beforePropEditor),delete l.dataset.errorMessage,l.classList.remove(y),l.classList.add(p),c.readonly=!1,c.tabIndex=0;var u=t.getAttachCellArea(n,r),h=u.element,d=u.rect;l.style.top="".concat(d.top.toFixed(),"px"),l.style.left="".concat(d.left.toFixed(),"px"),l.style.width="".concat(d.width.toFixed(),"px"),c.style.height="".concat(d.height.toFixed(),"px"),h.appendChild(l),c.value=o||"",c.style.font=t.font||"16px sans-serif";var v={grid:t,col:n,row:r,editor:e};this._onInputValue(c,v),s.IE?setTimeout((function(){return _(c,a)})):_(c,a),l.classList.add(y),l.classList.remove(p),c.readonly=!0,function(t,e,n,r){var o,i=r.classList,a=r.helperText;i&&(o=e.classList).add.apply(o,f(i)),a&&"function"!=typeof a&&(e.dataset.helperText=a),m(r,0,n)}(0,l,c,e),this._activeData=v,this._beforePropEditor=e,this._attaching=!0,setTimeout((function(){delete i._attaching}))}},{key:"detach",value:function(t){if(this._isActive()){var e=this._dialog,n=this._input;e.classList.remove(y),e.classList.add(p),n.readonly=!1,n.tabIndex=-1;var r=this._activeData,o=r.grid,i=r.col,a=r.row;o.invalidateCell(i,a),t&&o.focus()}this._activeData=null,this._beforeValue=null}},{key:"_doChangeValue",value:function(){var t=this;if(!this._isActive())return!1;var e=this._input,n=e.value;return l(this._validate(n),(function(r){if(r&&n===e.value){var o=t._activeData,i=o.grid,a=o.col,l=o.row;return i.doChangeValue(a,l,(function(){return n})),!0}return!1}))}},{key:"_isActive",value:function(){var t=this._dialog;return!(!t||!t.parentElement||!this._activeData)}},{key:"_bindDialogEvents",value:function(){var t=this,e=this._handler,n=this._dialog,r=this._input,o=function(t){return t.stopPropagation()};e.on(n,"click",o),e.on(n,"dblclick",o),e.on(n,"mousedown",o),e.on(n,"touchstart",o),e.on(r,"compositionstart",(function(t){r.classList.add("composition")})),e.on(r,"compositionend",(function(e){r.classList.remove("composition"),t._onInputValue(r)}));var s=function(e){r.classList.contains("composition")||t._onInputValue(r)};e.on(r,"keyup",s),e.on(r,"keypress",s),e.on(r,"keydown",(function(e){if(!r.classList.contains("composition")){var n=i(e);if(27===n)t.detach(!0),a(e);else if(13!==n||t._attaching)t._onInputValue(r);else{var o=t._input,s=o.value;l(t._doChangeValue(),(function(e){e&&s===o.value&&t.detach(!0)})),a(e)}}}))}},{key:"_onInputValue",value:function(t,e){var n=this._beforeValue,r=t.value;n!==r&&this._onInputValueChange(r,n,e),this._beforeValue=r}},{key:"_onInputValueChange",value:function(t,e,n){n=n||this._activeData;var r=this._dialog,o=n,i=o.grid,a=o.col,l=o.row,s=o.editor;if("function"==typeof s.helperText){var c=s.helperText(t,{grid:i,col:a,row:l});c?r.dataset.helperText=c:delete r.dataset.helperText}"errorMessage"in r.dataset&&this._validate(t,!0)}},{key:"_validate",value:function(t,e){var n=this._dialog,r=this._input,o=this._activeData,i=o.grid,a=o.col,s=o.row,c=o.editor,u=null;return c.inputValidator&&(u=c.inputValidator(t,{grid:i,col:a,row:s})),l(u,(function(o){return o||!c.validator||e||(o=c.validator(t,{grid:i,col:a,row:s})),l(o,(function(e){return e&&t===r.value?n.dataset.errorMessage=e:delete n.dataset.errorMessage,!e}))}))}}]),t}();t.exports=w},function(t,e,n){var r=n(83);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,"/*ms*/\n.cheetah-grid__small-dialog-input__input::-ms-clear {\n\tvisibility:hidden;\n}\n\n@keyframes cheetah-grid__small-dialog-input--hidden-animation {\n\t0% {\n\t\topacity: 1;\n\t}\n\t99% {\n\t\topacity: 1;\n\t}\n\t100% {\n\t\topacity: 0;\n\t}\n}\n\n.cheetah-grid__small-dialog-input {\n\tposition: absolute;\n\tbox-sizing: content-box;\n\tmargin : -1px auto auto -1px;\n\tborder-radius: 3px;\n\tbackground-color: #fafafa;\n\n\ttransition: padding 150ms ease-out, box-shadow 150ms ease-out;\n}\n.cheetah-grid__small-dialog-input--hidden {\n\tbox-shadow: none;\n\tpadding: 0;\n\n\tpointer-events: none;\n\tanimation: cheetah-grid__small-dialog-input--hidden-animation  150ms ease-out;\n\topacity: 0;\n}\n.cheetah-grid__small-dialog-input--shown {\n\tbox-shadow: 0 3px 1px -2px rgba(0, 0, 0, .2), 0 2px 2px 0px rgba(0, 0, 0, .14), 0 1px 5px 0px rgba(0, 0, 0, .12);\n\tpadding: 8px 24px;\n}\n.cheetah-grid__small-dialog-input__input {\n\twidth: 100%;\n\theight: 100%;\n\tbox-sizing: border-box;\n\tpadding: 3px 2px 0 4px;\n\tborder: none;\n\tborder-bottom: solid 1px rgba(0, 0, 0, .87);\n\toutline: none;\n\tbackground-color: transparent;\n\ttransition: all 300ms ease-out;\n}\n.cheetah-grid__small-dialog-input__input:focus {\n\tborder-bottom: solid 1px #2196f3;\n\tbox-shadow: 0 1px 0 0 #2196f3;\n}\n.cheetah-grid__small-dialog-input::after {\n\tcontent: '';\n\tfont-family: Roboto;\n\tfont-size: 12px;\n\tfont-size: .75rem;\n\tmin-height: 1em;\n\tline-height: 1;\n\tdisplay: block;\n\twidth: 100%;\n\tpadding-top: 8px;\n}\n.cheetah-grid__small-dialog-input.helper-text--right-justified::after {\n\ttext-align: right;\n}\n.cheetah-grid__small-dialog-input[data-helper-text]::after {\n\tcontent: attr(data-helper-text);\n\tcolor: rgba(0, 0, 0, .87);\n}\n.cheetah-grid__small-dialog-input[data-error-message] input {\n\tborder-bottom: solid 1px #ff1744;\n\tbox-shadow: 0 1px 0 0 #ff1744;\n}\n.cheetah-grid__small-dialog-input[data-error-message]::after {\n\tcontent: attr(data-error-message);\n\tcolor: #ff1744;\n\ttext-align: left;\n}",""])},function(t,e,n){(function(e){var r=n(0).obj.setReadonly,o=n(40),i=n(85),l=n(1).INLINE_INPUT_EDITOR_STATE_ID,u=null,f=0;function d(t,e,n,o){t[l]||r(t,l,{}),u||(u=new i);var a=t[l];a.element||(a.element=u,f++,t.addDisposable({dispose:function(){--f||(u.dispose(),u=null)}})),u.attach(t,n,e.col,e.row,o)}function g(t){u&&u.detach(t)}function p(t){u&&u.doChangeValue()}var y=function(t){function n(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,n),(t=a(this,s(n).call(this,e)))._classList=e.classList,t._type=e.type,t}return c(n,t),v(n,[{key:"clone",value:function(){return new n(this)}},{key:"onInputCellInternal",value:function(t,e,n){d(t,e,this,n)}},{key:"onOpenCellInternal",value:function(t,e){var n=this;t.doGetCellValue(e.col,e.row,(function(r){d(t,e,n,r)}))}},{key:"onChangeSelectCellInternal",value:function(t,e,n){p(),g()}},{key:"onGridScrollInternal",value:function(t){p(),g(!0)}},{key:"onChangeDisabledInternal",value:function(){g(!0)}},{key:"onChangeReadOnlyInternal",value:function(){g(!0)}},{key:"onSetInputAttrsInternal",value:function(t,e,n){i.setInputAttrs(this,t,n)}},{key:"classList",get:function(){return this._classList&&(e(this._classList)?this._classList:[this._classList])},set:function(t){this._classList=t}},{key:"type",get:function(){return this._type},set:function(t){this._type=t}}]),n}(o);t.exports=y}).call(this,n(2).Array_isArray)},function(t,e,n){var r=n(0).event,o=r.getKeyCode,i=r.cancel,a=n(3),l=n(10).createElement,s="cheetah-grid__inline-input";function c(t,e,n){var r,o=t.classList,i=t.type;o&&(r=n.classList).add.apply(r,f(o)),n.type=i||""}var u=function(){function t(){h(this,t),this._handler=new a,this._input=(n(86),l("input",{classList:s})),this._bindInputEvents()}return v(t,null,[{key:"setInputAttrs",value:function(){c.apply(void 0,arguments)}}]),v(t,[{key:"dispose",value:function(){this.detach(),this._handler.dispose(),this._input=null,this._beforePropEditor=null}},{key:"attach",value:function(t,e,n,r,o){var i=this,a=this._input,l=this._handler;if(this._beforePropEditor){var s,u=this._beforePropEditor.classList;u&&(s=a.classList).remove.apply(s,f(u))}a.style.font=t.font||"16px sans-serif";var h=t.getAttachCellArea(n,r),d=h.element,v=h.rect;a.style.top="".concat(v.top.toFixed(),"px"),a.style.left="".concat(v.left.toFixed(),"px"),a.style.width="".concat(v.width.toFixed(),"px"),a.style.height="".concat(v.height.toFixed(),"px"),d.appendChild(a),c(e,0,a),a.value=o,this._activeData={grid:t,col:n,row:r,editor:e},this._beforePropEditor=e,l.tryWithOffEvents(a,"blur",(function(){!function(){a.focus();var t=a.value.length;try{if(void 0!==a.selectionStart)return a.selectionStart=t,void(a.selectionEnd=t)}catch(t){}if(document.selection){var e=a.createTextRange();e.collapse(),e.moveEnd("character",t),e.moveStart("character",t),e.select()}}()})),this._attaching=!0,setTimeout((function(){delete i._attaching}))}},{key:"detach",value:function(t){if(this._isActive()){var e=this._activeData,n=e.grid,r=e.col,o=e.row,i=this._input;this._handler.tryWithOffEvents(i,"blur",(function(){i.parentElement.removeChild(i)})),n.invalidateCell(r,o),t&&n.focus()}this._activeData=null}},{key:"doChangeValue",value:function(){if(this._isActive()){var t=this._input.value,e=this._activeData,n=e.grid,r=e.col,o=e.row;n.doChangeValue(r,o,(function(){return t}))}}},{key:"_isActive",value:function(){var t=this._input;return!(!t||!t.parentElement||!this._activeData)}},{key:"_bindInputEvents",value:function(){var t=this,e=this._handler,n=this._input,r=function(t){return t.stopPropagation()};e.on(n,"click",r),e.on(n,"mousedown",r),e.on(n,"touchstart",r),e.on(n,"dblclick",r),e.on(n,"compositionstart",(function(t){n.classList.add("composition")})),e.on(n,"compositionend",(function(t){n.classList.remove("composition")})),e.on(n,"keydown",(function(e){if(!n.classList.contains("composition")&&13===o(e)){if(!t._isActive()||t._attaching)return;var r=t._activeData.grid;t.doChangeValue(),r&&r.focus(),t.detach(),i(e)}})),e.on(n,"blur",(function(e){t.doChangeValue(),t.detach()}))}}]),t}();t.exports=u},function(t,e,n){var r=n(87);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,"/*ms*/\n.cheetah-grid__inline-input::-ms-clear {\n\tvisibility:hidden;\n}\n\n\n.cheetah-grid__inline-input {\n\tposition: absolute;\n\tbox-sizing: border-box;\n}\n",""])},function(t,e,n){(function(e){var r=n(0),o=r.obj.setReadonly,i=r.array.find,l=r.event.cancel,u=r.then,f=n(15),d=f.isDisabledRecord,g=f.isReadOnlyRecord,p=n(41).normalize,y=n(16),_=n(5).EVENT_TYPE,m=_.SELECTED_CELL,w=_.CLICK_CELL,C=_.KEYDOWN,x=_.SCROLL,b=_.MOUSEOVER_CELL,k=_.MOUSEOUT_CELL,E=_.MOUSEMOVE_CELL,I=_.PASTE_CELL,L=n(89),S=n(1).INLINE_MENU_EDITOR_STATE_ID,T=null,A=0;function R(t){T&&T.detach(t)}var O=function(t){function n(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,n),(t=a(this,s(n).call(this,e)))._classList=e.classList,t._options=p(e.options),t}return c(n,t),v(n,[{key:"dispose",value:function(){}},{key:"clone",value:function(){return new n(this)}},{key:"onChangeDisabledInternal",value:function(){R(!0)}},{key:"onChangeReadOnlyInternal",value:function(){R(!0)}},{key:"bindGridEvent",value:function(t,e,n){var r=this,a=function(e){g(r.readOnly,t,e.row)||d(r.disabled,t,e.row)||t.doGetCellValue(e.col,e.row,(function(n){!function(t,e,n,r){t[S]||o(t,S,{}),T||(T=new L);var i=t[S];i.element||(i.element=T,A++,t.addDisposable({dispose:function(){--A||(T.dispose(),T=null)}})),T.attach(t,n,e.col,e.row,r)}(t,e,r,n)}))};return[t.listen(w,(function(t){n.isTarget(t.col,t.row)&&a({col:t.col,row:t.row})})),t.listen(C,(function(e,r){if(113===e||13===e){var o=t.selection.select;n.isTarget(o.col,o.row)&&a({col:o.col,row:o.row})}})),t.listen(m,(function(t){R()})),t.listen(x,(function(){R(!0)})),t.listen(b,(function(e){g(r.readOnly,t,e.row)||d(r.disabled,t,e.row)||n.isTarget(e.col,e.row)&&(t.getElement().style.cursor="pointer")})),t.listen(E,(function(e){g(r.readOnly,t,e.row)||d(r.disabled,t,e.row)||n.isTarget(e.col,e.row)&&(t.getElement().style.cursor||(t.getElement().style.cursor="pointer"))})),t.listen(k,(function(e){n.isTarget(e.col,e.row)&&(t.getElement().style.cursor="")})),t.listen(I,(function(e){if(!e.multi&&n.isTarget(e.col,e.row)){var o=e.normalizeValue.trim(),a=i(r._options,(function(t){return"".concat(t.value).trim()===o}));a&&(l(e.event),u(t.doChangeValue(e.col,e.row,(function(){return a.value})),(function(){return t.invalidateCell(e.col,e.row)})))}}))]}},{key:"classList",get:function(){return this._classList&&(e(this._classList)?this._classList:[this._classList])},set:function(t){this._classList=t}},{key:"options",get:function(){return this._options},set:function(t){this._options=p(t)}}]),n}(y);t.exports=O}).call(this,n(2).Array_isArray)},function(e,n,r){(function(n){var o=r(0),i=o.isDef,a=o.event,l=a.getKeyCode,s=a.cancel,c=r(3),u=r(10),d=u.createElement,g=u.appendHtml,p=u.empty,y=u.disableFocus,_=u.isFocusable,m=u.findPrevSiblingFocusable,w=u.findNextSiblingFocusable,C="cheetah-grid__inline-menu",x="".concat(C,"__menu-item"),b="".concat(C,"--hidden"),k="".concat(C,"--shown"),E="".concat(x,"--empty");function I(t){for(var e=t;!e.classList.contains(x);)if(!(e=e.parentElement)||e.classList.contains(C))return;return e}function L(t,e,n){n.style.top="".concat(e.top.toFixed(),"px"),n.style.left="".concat(e.left.toFixed(),"px"),n.style.width="".concat(e.width.toFixed(),"px"),n.style["line-height"]="".concat(e.height.toFixed(),"px"),t.appendChild(n)}function S(e,r,o,a,l,s){var c=r.options,u=r.classList;s.classList.remove(k),s.classList.add(b),p(s),s.style.font=e.font||"16px sans-serif";var h=null,v=null;c.forEach((function(t,e){var r=function(t,e){var r,o=t.classList,a=t.caption,l=t.value,s=t.html,c=d("li",{classList:x});if(c.tabIndex=0,c.dataset.valueindex=e,o&&(r=c.classList).add.apply(r,f(n(o)?o:[o])),a){var u=d("span",{text:a});c.appendChild(u)}else s&&g(c,s);return""!==l&&i(l)||c.classList.add(E),c}(t,e);s.appendChild(r),t.value===l&&(v=r,r.dataset.select=!0),r.classList.contains(E)&&(h=r)}));var y,_=v||h||s.children[0];u&&(y=s.classList).add.apply(y,f(u));var m=Array.prototype.slice.call(s.children,0),w=m.indexOf(_),C=e.getAttachCellArea(o,a),I=C.element,S=C.rect;S.width++,L(I,S,s);for(var T=0,A=0,R=0;R<m.length;R++){var O=m[R].offsetHeight;R<w&&(T+=O),A+=O}S.offsetTop(-T),s.style["transform-origin"]="center ".concat(T+Math.ceil(m[w].offsetHeight/2),"px 0px"),L(I,S,s);var B=s.getBoundingClientRect(),M=(A-B.height)/2,D=B.top-M,P=D,F=P+A,z=t.innerHeight;F>z-20&&(P-=F-z+20),P<20&&(P=20),P!==D&&(S.offsetTop(-(D-P)),L(I,S,s)),_&&_.focus(),s.classList.remove(b),s.classList.add(k)}var T=function(){function t(){h(this,t),this._handler=new c,this._menu=(r(90),d("ul",{classList:C})),this._bindMenuEvents()}return v(t,[{key:"dispose",value:function(){var t=this._menu;this.detach(),this._handler.dispose(),this._menu=null,this._beforePropEditor=null,t.parentElement&&t.parentElement.removeChild(t)}},{key:"attach",value:function(t,e,n,r,o){var i=this._menu;if(this._beforePropEditor){var a,l=this._beforePropEditor.classList;l&&(a=i.classList).remove.apply(a,f(l))}S(t,e,n,r,o,i),this._activeData={grid:t,col:n,row:r,editor:e},this._beforePropEditor=e}},{key:"detach",value:function(t){if(this._isActive()){var e=this._activeData,n=e.grid,r=e.col,o=e.row;!function(t,e,n,r){r.classList.remove(k),r.classList.add(b),y(r)}(0,0,0,this._menu),n.invalidateCell(r,o),t&&n.focus()}this._activeData=null}},{key:"_doChangeValue",value:function(t){if(this._isActive()){var e=this._activeData,n=e.grid,r=e.col,o=e.row,i=e.editor.options[t].value;n.doChangeValue(r,o,(function(){return i}))}}},{key:"_isActive",value:function(){var t=this._menu;return!(!t||!t.parentElement||!this._activeData)}},{key:"_bindMenuEvents",value:function(){var t=this,e=this._handler,n=this._menu,r=function(t){return t.stopPropagation()};e.on(n,"mousedown",r),e.on(n,"touchstart",r),e.on(n,"dblclick",r),e.on(n,"click",(function(e){e.stopPropagation();var n=I(e.target);if(n){var r=n.dataset.valueindex;t._doChangeValue(r),t.detach(!0)}})),e.on(n,"keydown",(function(e){var r=I(e.target);if(r){var o=l(e);if(13===o){var i=r.dataset.valueindex;t._doChangeValue(i),t.detach(!0),s(e)}else if(27===o)t.detach(!0),s(e);else if(38===o){var a=m(r);a&&(a.focus(),s(e))}else if(40===o){var c=w(r);c&&(c.focus(),s(e))}else if(9===o)if(e.shiftKey){if(!m(r)){var u=n.querySelectorAll(".".concat(x)),f=u[u.length-1];_(f)||(f=m(f)),f&&(f.focus(),s(e))}}else if(!w(r)){var h=n.querySelector(".".concat(x));_(h)||(h=w(h)),h&&(h.focus(),s(e))}}}))}}]),t}();e.exports=T}).call(this,r(2).Array_isArray)},function(t,e,n){var r=n(91);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,'.cheetah-grid__inline-menu {\n\tposition: absolute;\n\tcolor: rgba(0, 0, 0, .87);\n\tbox-sizing: content-box;\n\tmargin: -1px auto auto -1px;\n\tpadding: 8px 0;\n\tbackground-color: #fafafa;\n\tlist-style-type: none;\n\tborder-radius: 2px;\n\tmax-height: calc(100vh - 40px);\n\toverflow-y: auto;\n\n}\n.cheetah-grid__inline-menu--hidden {\n\ttransform: scale(.9);\n\tbox-shadow: none;\n\topacity: 0;\n\n\tpointer-events: none;\n\n\ttransition: all 50ms ease-out;\n}\n.cheetah-grid__inline-menu--hidden * {\n\tpointer-events: none;\n}\n.cheetah-grid__inline-menu--shown {\n\ttransform: translateY(-7px);\n\tbox-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0px rgba(0, 0, 0, 0.14), 0 1px 5px 0px rgba(0, 0, 0, 0.12);\n\topacity: 1;\n\n\ttransition: all 150ms ease-out;\n}\n\n.cheetah-grid__inline-menu__menu-item {\n\theight: 100%;\n\tbox-sizing: border-box;\n\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-start;\n\n\toutline: none;\n\tcursor: pointer;\n\n\tposition: relative;\n\toverflow: hidden;\n\n\tpadding: 0 16px;\n}\n.cheetah-grid__inline-menu__menu-item--empty {\n\tcolor: rgba(0, 0, 0, .38);\n}\n\n.cheetah-grid__inline-menu__menu-item::before {\n\tcontent: "";\n\tposition: absolute;\n\ttop: -50%;\n\tleft: -50%;\n\twidth: 200%;\n\theight: 200%;\n\tbackground-color: #000;\n\topacity: 0;\n\tpointer-events: none;\n\ttransition: opacity 15ms linear;\n}\n.cheetah-grid__inline-menu__menu-item:hover::before {\n\topacity: .04;\n}\n.cheetah-grid__inline-menu__menu-item[data-select]::before {\n\topacity: .04;\n}\n.cheetah-grid__inline-menu__menu-item:focus::before {\n\topacity: .12;\n}',""])},function(t,e,n){var r=n(11),o=n(93),i=n(94),a=n(95),l=n(96),s=n(98),c=n(99),u=n(100),f=n(102),h=n(103),d={TYPES:{DEFAULT:new r,NUMBER:new o,CHECK:new i,BUTTON:new a,IMAGE:new l,MULTILINETEXT:new h},get Column(){return r},get NumberColumn(){return o},get CheckColumn(){return i},get ButtonColumn(){return a},get ImageColumn(){return l},get PercentCompleteBarColumn(){return s},get IconColumn(){return c},get BranchGraphColumn(){return u},get MenuColumn(){return f},get MultilineTextColumn(){return h},of:function(t){return t?"string"==typeof t?d.TYPES[t.toUpperCase()]||d.of(null):t:d.TYPES.DEFAULT}};t.exports=d},function(t,e,n){var r,o=n(11),i=n(43),l=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._format=n.format,t}return c(e,t),v(e,null,[{key:"defaultFotmat",get:function(){return r||(r=new Intl.NumberFormat)},set:function(t){r=t}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"withFormat",value:function(t){var e=this.clone();return e._format=t,e}},{key:"convertInternal",value:function(t){return isNaN(t-0)?t:(this._format||e.defaultFotmat).format(t-0)}},{key:"StyleClass",get:function(){return i}},{key:"format",get:function(){return this._format}}]),e}(o);t.exports=l},function(t,e,n){var r=n(0).isDef,o=n(9),i=n(44),l=n(1).CHECK_COLUMN_STATE_ID,u=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"convertInternal",value:function(t){return function(t){if("string"==typeof t){if("false"===t)return!1;if("off"===t)return!1;if(t.match(/^0+$/))return!1}return t}(t)}},{key:"drawInternal",value:function(t,e,n,o,i,a){var s=a.drawCellBase,c=n.textAlign,u=n.textBaseline,f=n.borderColor,h=n.checkBgColor,d=n.uncheckBgColor,v=n.bgColor;v&&s({bgColor:v});var g=e.col,p=e.row,y="".concat(g,":").concat(p),_="".concat(y,"::elapsed"),m=i[l]&&i[l][_],w={textAlign:c,textBaseline:u,borderColor:f,checkBgColor:h,uncheckBgColor:d};r(m)&&(w.animElapsedTime=m),o.checkbox(t,e,w)}},{key:"bindGridEvent",value:function(t,e,n){return[]}},{key:"StyleClass",get:function(){return i}}]),e}(o);t.exports=u},function(t,e,n){var r=n(11),o=n(45),i=n(1).BUTTON_COLUMN_STATE_ID,l=n(17),u=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._caption=n.caption,t}return c(e,t),v(e,[{key:"withCaption",value:function(t){var e=this.clone();return e._caption=t,e}},{key:"clone",value:function(){return new e(this)}},{key:"convertInternal",value:function(t){return this._caption||t}},{key:"drawInternal",value:function(t,e,n,r,o,a){var s=a.drawCellBase,c=a.getIcon,u=n.textAlign,f=n.textBaseline,h=n.bgColor,d=n.color,v=n.buttonBgColor,g=n.font,p=n.padding,y=n.textOverflow;h&&s({bgColor:h}),r.testFontLoad(g,t,e);var _=e.col,m=e.row,w=!1,C=o[i];C&&(C.mouseActiveCell&&C.mouseActiveCell.col===_&&C.mouseActiveCell.row===m?w=!0:e.getSelectState().selected&&(w=!0)),l.loadIcons(c(),e,r,(function(e,n){r.button(t,n,{textAlign:u,textBaseline:f,bgColor:v,color:d,font:g,padding:p,shadow:!w||{color:"rgba(0, 0, 0, 0.48)",blur:6,offsetY:3},textOverflow:y,icons:e})}))}},{key:"StyleClass",get:function(){return o}},{key:"caption",get:function(){return this._caption}}]),e}(r);t.exports=u},function(t,e,r){var o=r(46),i=r(51).getCacheOrLoad,l=r(8).calcStartPosition,u=r(9),f=50,d=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"onDrawCell",value:function(t,r,o,a){return n(s(e.prototype),"onDrawCell",this).call(this,i("ImageColumn",f,t),r,o,a)}},{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,e,n,r,o,i){var a=i.drawCellBase;if(t){var s=n.textAlign,c=n.textBaseline,u=n.margin,f=n.bgColor;f&&a({bgColor:f}),r.drawWithClip(e,(function(r){r.textAlign=s,r.textBaseline=c;var o=e.getRect();if("keep-aspect-ratio"===n.imageSizing){var i=function(t,e,n,r){var o=t,i=e;return o>n&&(i=(o=n)*e/t),i>r&&(o=(i=r)*t/e),{width:o,height:i}}(t.width,t.height,o.width-2*u,o.height-2*u),a=i.width,f=i.height,h=l(r,o,a,f,{offset:u});r.drawImage(t,0,0,t.width,t.height,h.x,h.y,a,f)}else r.drawImage(t,0,0,t.width,t.height,o.left+u,o.top+u,o.width-2*u,o.height-2*u)}))}}},{key:"StyleClass",get:function(){return o}}]),e}(u);t.exports=d},function(t,e,n){var r=function(){function t(e){h(this,t),this._list=[],this._map={},this._cacheSize=e||50}return v(t,[{key:"get",value:function(t){var e=this._map[t];if(e){var n=this._list,r=n.indexOf(t);n.splice(r,1),n.push(t)}return e}},{key:"put",value:function(t,e){var n=this._list,r=this._map;if(r[t]){var o=n.indexOf(t);n.splice(o,1)}r[t]=e,n.push(t),n.length>this._cacheSize&&delete r[n.shift()]}}]),t}();t.exports=r},function(t,e,r){var o=r(48),i=r(0),l=i.getOrApply,u=i.str,f=r(11),d=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._min=n.min||0,t._max=n.max||t._min+100,t._formatter=n.formatter||function(t){return t},t}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,r,o,i,a,c){n(s(e.prototype),"drawInternal",this).call(this,this._formatter(t),r,o,i,a,c);var f=o.barColor,h=o.barBgColor,d=o.barHeight,v="".concat(t);u.endsWith(v,"%")&&(v=v.substr(0,v.length-1));var g=v-0,p=g<this._min?0:g>this._max?1:(g-this._min)/(this._max-this._min);i.drawWithClip(r,(function(t){var e=r.getRect(),n=e.width-4-1,o=e.bottom-2-d-1,i=e.left+2;t.fillStyle=l(h,100*p)||"#f0f3f5",t.beginPath(),t.rect(i,o,n,d),t.fill();var a=Math.min(n*p,n);t.fillStyle=l(f,100*p)||"#20a8d8",t.beginPath(),t.rect(i,o,a,d),t.fill()}))}},{key:"StyleClass",get:function(){return o}}]),e}(f);t.exports=d},function(t,e,r){var o=r(47),i=r(11),l=r(25),u=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._tagName=n.tagName||"i",t._className=n.className,t._content=n.content,t._name=n.name,t._iconWidth=n.iconWidth,t}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,r,o,i,a,c){var u=t-0;if(isNaN(u))c.getIcon=function(){return null};else{var f={};l.iconPropKeys.forEach((function(t){f[t]=o[t]})),f.className=this._className,f.tagName=this._tagName,f.content=this._content,f.name=this._name,this._iconWidth&&(f.width=this._iconWidth),c.getIcon=function(){return function(t,e){e===1/0&&(e=0);for(var n=[],r=0;r<e;r++)n.push(t);return n}(f,u)}}n(s(e.prototype),"drawInternal",this).call(this,"",r,o,i,a,c)}},{key:"StyleClass",get:function(){return o}}]),e}(i);t.exports=u},function(t,e,r){(function(e){var o=r(0),i=o.isDef,l=o.getOrApply,u=o.isPromise,d=o.obj.isObject,g=r(101),p=r(9),y=r(1).BRANCH_GRAPH_COLUMN_STATE_ID,_=function t(e){var n=e.fromIndex,r=e.toIndex,o=e.colorIndex,i=e.point;h(this,t),this.fromIndex=n,this.toIndex=r,this.colorIndex=o,this.point=i},m=function(){function t(e){var n=e.index,r=e.commit,o=void 0!==r&&r,i=e.lines,a=void 0===i?[]:i,l=e.tag;h(this,t),this.index=n,this.commit=o,this.lines=a,this.tag=l}return v(t,null,[{key:"mergeLines",value:function(t){var e=t.filter((function(t){return i(t.fromIndex)&&i(t.toIndex)})),n=t.filter((function(t){return i(t.fromIndex)&&!i(t.toIndex)})),r=t.filter((function(t){return!i(t.fromIndex)&&i(t.toIndex)}));return n.forEach((function(t){for(var n=0;n<r.length;n++){var o=r[n];if(!o.point&&t.colorIndex===o.colorIndex){t.toIndex=o.toIndex,r.splice(n,1);break}}e.push(t)})),e.concat(r)}},{key:"merge",value:function(e,n){return e?new t({index:e.index,commit:e.commit||n.commit,lines:t.mergeLines(e.lines.concat(n.lines)),tag:e.tag||n.tag}):n}}]),t}();function w(t,e){for(var n=f(t).reverse(),r=0;r<n.length;r++){var o=n[r][e];if(o){o.lines=m.mergeLines(o.lines.concat([new _({toIndex:e,colorIndex:e})]));for(var i=0;i<r;i++)n[i][e]=new m({index:e,lines:[new _({fromIndex:e,toIndex:e,colorIndex:e})]});return!0}}return!1}function C(t,n){var r=t.timeline,o=[];(e(n)?n:[n]).forEach((function(e){if(e){var n;if("branch"===e.command){var r=d(e.branch)?e.branch.from:null,i=d(e.branch)?e.branch.to:e.branch;n=function(t,e,n){var r=t.timeline,o=t.branches,i=o.indexOf(e),a=o.indexOf(n);if(a<0&&(a=o.length,o.push(n)),i<0)return new m({index:a});var l=function(){for(var t=r.length-1;t>=0;t--){var e=r[t][i];if(e&&e.commit)return t}return-1}();if(-1===l)return null;var s,c,u=l+1,f=new m({index:a,lines:[new _({fromIndex:i,colorIndex:a})]});if(u<r.length){var h=r[u];s=h[a]=m.merge(h[a],f)}else s=f,c=f;var d=r[l][i];return d.lines=m.mergeLines(d.lines.concat([new _({toIndex:a,colorIndex:a,point:s})])),c}(t,r,i)}else if("commit"===e.command){var a=e.branch;n=function(t,e){var n=t.timeline,r=t.branches.indexOf(e);if(r<0)return null;var o=new m({index:r,commit:!0});return w(n,r)&&(o.lines=m.mergeLines(o.lines.concat([new _({fromIndex:r,colorIndex:r})]))),o}(t,a)}else if("merge"===e.command){var l=e.branch,s=l.from,c=l.to;n=function(t,e,n){var r=t.timeline,o=t.branches,i=o.indexOf(e),a=o.indexOf(n);if(a<0||i<0)return new m({index:a,commit:!0});var l=new m({index:a,commit:!0,lines:[new _({fromIndex:i,colorIndex:i}),new _({fromIndex:a,colorIndex:a})]}),s=f(r),c=s.pop();return c&&(c[i]=m.merge(c[i],new m({index:a,lines:[new _({toIndex:a,colorIndex:i})]}))),w(s,i)&&c&&(c[i].lines=m.mergeLines(c[i].lines.concat([new _({fromIndex:i,colorIndex:i})]))),w(r,a),l}(t,s,c)}else if("tag"===e.command){var u=e.branch,h=e.tag;n=function(t,e,n){t.timeline;var r=t.branches,o=r.indexOf(e);return o<0&&(o=r.length,r.push(e)),new m({index:o,tag:n})}(t,u,h)}n&&n.index>-1&&(o[n.index]=m.merge(o[n.index],n))}})),r.push(o)}function x(t,e,n){var r={branches:[],timeline:[]};return function(t,e,n){var r=t.dataSource,o=t.getField(e);d(o)&&o.get&&o.set&&(o=o.get);for(var i,a=[],l=function(t){var e=r.getField(t,o);if(u(e)){var n=a.length;a.push(e),i=i?i.then((function(){return e})).then((function(t){a[n]=t})):e.then((function(t){a[n]=t}))}else a.push(e)},s=0;s<r.length;s++)l(s);i?i.then((function(){return n(a)})):n(a)}(e,n,(function(e){"top"!==t&&(e=f(e).reverse()),e.forEach((function(t){C(r,t)}))})),r}var b=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._start=n.start||"bottom",t._cache=!!i(n.cache)&&n.cache,t}return c(e,t),v(e,[{key:"clearCache",value:function(t){delete t[y]}},{key:"onDrawCell",value:function(t,r,o,i){if(this._cache){var a=i[y]||(i[y]={}),l=o.col;a[l]||(a[l]=x(this._start,i,l))}return n(s(e.prototype),"onDrawCell",this).call(this,t,r,o,i)}},{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,e,n,r,o,a){var s=a.drawCellBase,c=e.col,u=e.row,f=this._cache&&o[y]?o[y][c]:x(this._start,o,c),h=f.timeline,d=f.branches,v="top"!==this._start?{upLineIndexKey:"toIndex",downLineIndexKey:"fromIndex"}:{upLineIndexKey:"fromIndex",downLineIndexKey:"toIndex"},g=v.upLineIndexKey,p=v.downLineIndexKey,_="top"!==this._start?h[h.length-(u-o.frozenRowCount)-1]:h[u-o.frozenRowCount],m=n.branchColors,w=n.branchLineWidth,C=n.circleSize,b=n.mergeStyle,k=n.margin,E=n.bgColor;E&&s({bgColor:E});var I=e.getRect(),L=C/2,S=I.width-2*k;r.drawWithClip(e,(function(t){t.textAlign="left",t.textBaseline="middle";var e=function(t,e,n,r,o,i){var a=Math.max(n/o.length+1,5);i.forEach((function(e){e.forEach((function(e,o){if(!(o<=0)&&e.tag){var i=t.measureText(e.tag).width;a*o+2*r+4+i>n&&(a=Math.max((n-2*r-4-i)/o,5))}}))}));var l=[],s=e;return o.forEach((function(){l.push(Math.ceil(s+r)),s+=a})),l}(t,I.left+k,S,L,d,h),n=I.top+I.height/2;_.map((function(t,e){return t?t.lines.map((function(t){return{colorIndex:t.colorIndex,upLineIndex:t[g],downLineIndex:t[p],pointIndex:e}})):[]})).reduce((function(t,e){return t.concat(e)}),[]).sort((function(t,e){return e.colorIndex-t.colorIndex})).forEach((function(r){var a=e[r.pointIndex];!function(t,e,n,r,o,a,s,c,u){var f=c.branchXPoints,h=(c.margin,c.branchColors),d=c.branchLineWidth,v=c.mergeStyle,g=(u.width,u.col),p=u.row,y=u.branches;if(i(o)||i(a)){if(e.strokeStyle=l(h,y[s],s),e.lineWidth=d,e.lineCap="round",e.beginPath(),i(o)){var _=f[o],m=t.getCellRelativeRect(g,p-1),w=m.top+m.height/2;e.moveTo(_,w),"bezier"===v?e.bezierCurveTo(_,(r+w)/2,n,(r+w)/2,n,r):e.lineTo(n,r)}else e.moveTo(n,r);if(i(a)){var C=f[a],x=t.getCellRelativeRect(g,p+1),b=x.top+x.height/2;"bezier"===v?e.bezierCurveTo(n,(r+b)/2,C,(r+b)/2,C,b):e.lineTo(C,b)}e.stroke()}}(o,t,a,n,r.upLineIndex,r.downLineIndex,r.colorIndex,{margin:k,branchXPoints:e,branchLineWidth:w,branchColors:m,mergeStyle:b},{width:S,col:c,row:u,branches:d})})),_.forEach((function(r,o){if(r&&r.commit){var i=e[o];t.fillStyle=l(m,d[o],o),t.beginPath(),t.arc(i,n,L,0,2*Math.PI,!0),t.fill(),t.closePath()}})),_.forEach((function(r,o){r&&r.tag&&(t.fillStyle=l(m,d[o],o),t.fillText(r.tag,e[o]+L+4,n))}))}))}},{key:"StyleClass",get:function(){return g}}]),e}(p);t.exports=b}).call(this,r(2).Array_isArray)},function(t,e,n){var r,o=n(24),i=function(t,e){switch(e%3){case 0:return"#979797";case 1:return"#008fb5";case 2:return"#f1c109"}return"#979797"},l=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,function(t){var e=t.textAlign,n=void 0===e?"center":e;return t.textAlign=n,t}(n))))._branchColors=n.branchColors||i,t._margin=n.margin||4,t._circleSize=n.circleSize||16,t._branchLineWidth=n.branchLineWidth||4,t._mergeStyle="straight"===n.mergeStyle?"straight":"bezier",t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"branchColors",get:function(){return this._branchColors},set:function(t){this._branchColors=t,this.doChangeStyle()}},{key:"margin",get:function(){return this._margin},set:function(t){this._margin=t,this.doChangeStyle()}},{key:"circleSize",get:function(){return this._circleSize},set:function(t){this._circleSize=t,this.doChangeStyle()}},{key:"branchLineWidth",get:function(){return this._branchLineWidth},set:function(t){this._branchLineWidth=t,this.doChangeStyle()}},{key:"mergeStyle",get:function(){return this._mergeStyle},set:function(t){this._mergeStyle=t,this.doChangeStyle()}}]),e}(o);t.exports=l},function(t,e,r){var o=r(41).normalize,i=r(0).isDef,l=r(9),u=r(50),f=r(17),d=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._options=o(n.options),t}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"withOptions",value:function(t){var e=this.clone();return e._options=o(t),e}},{key:"drawInternal",value:function(t,e,n,r,o,a){var l=a.drawCellBase,s=a.getIcon,c=n.textAlign,u=n.textBaseline,h=n.font,d=n.bgColor,v=n.padding,g=n.textOverflow,p=n.appearance,y=n.color;d&&l({bgColor:d});var _=this._convertInternal(t);r.testFontLoad(h,_,e),f.loadIcons(s(),e,r,(function(e,n){var o=r.toBoxPixelArray(v||0,n,h),a=o.slice(0);a[1]+=26;var l=o.slice(0);l[1]+=8,i(y)||i(t)&&""!==t||(y="rgba(0, 0, 0, .38)"),r.text(_,n,{textAlign:c,textBaseline:u,color:y,font:h,padding:a,textOverflow:g,icons:e}),"menulist-button"===p?r.text("",n,{textAlign:"right",textBaseline:u,color:y,font:h,icons:[{path:"M0 2 5 7 10 2z",width:10,color:"rgba(0, 0, 0, .54)"}],padding:l}):"none"!==p&&console.warn("unsupported appearance:".concat(p))}))}},{key:"convertInternal",value:function(t){return t}},{key:"_convertInternal",value:function(t){for(var r=this._options,o=0;o<r.length;o++){var i=r[o];if(i.value===t){t=i.caption;break}}return n(s(e.prototype),"convertInternal",this).call(this,t)}},{key:"StyleClass",get:function(){return u}},{key:"options",get:function(){return this._options}}]),e}(l);t.exports=d},function(t,e,n){var r=n(49),o=n(9),i=n(17),l=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),a(this,s(e).call(this,t))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,e,n,r,o,a){var l=a.drawCellBase,s=a.getIcon,c=n.textAlign,u=n.textBaseline,f=n.color,h=n.font,d=n.bgColor,v=n.padding,g=n.lineHeight,p=n.autoWrapText,y=n.lineClamp,_=n.textOverflow;d&&l({bgColor:d});var m=t.replace(/\r?\n/g,"\n").replace(/\r/g,"\n").split("\n");r.testFontLoad(h,t,e),i.loadIcons(s(),e,r,(function(t,e){r.multilineText(m,e,{textAlign:c,textBaseline:u,color:f,font:h,padding:v,lineHeight:g,autoWrapText:p,lineClamp:y,textOverflow:_,icons:t})}))}},{key:"StyleClass",get:function(){return r}}]),e}(o);t.exports=l},function(t,e,n){var r=n(52),o=n(54),i=n(12);t.exports={action:r,type:o,style:i}},function(t,e,n){var r=n(53).bindCellClickAction,o=n(26),i=n(0).obj.isObject,l=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._sort=n.sort,t}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"_executeSort",value:function(t,e){if("function"==typeof this._sort)this._sort({order:t.order,col:t.col,grid:e});else{var n=e.getField(t.col);i(n)&&n.get&&n.set&&(n=n.get),e.dataSource.sort(n,t.order)}}},{key:"bindGridEvent",value:function(t,e){var n=this;return f(r(t,e,{action:function(r){if(!n.disabled){var o,i=t.sortState;o=e.isCellInRange(i.col,r.row)?{col:e.startCol,row:e.startRow,order:"asc"===i.order?"desc":"asc"}:{col:e.startCol,row:e.startRow,order:"asc"},t.sortState=o,n._executeSort(o,t),t.invalidateGridRect(0,0,t.colCount-1,t.rowCount-1)}},mouseOver:function(t){return!n.disabled}}))}},{key:"sort",get:function(){return this._sort},set:function(t){this._sort=t,this.onChangeDisabledInternal()}}]),e}(o);t.exports=l},function(t,e,n){var r=n(0).obj.setReadonly,o=n(53),i=o.bindCellClickAction,l=o.bindCellKeyAction,u=n(22),d=n(26),g=n(1).CHECK_HEADER_STATE_ID,p=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"bindGridEvent",value:function(t,e){var n=this;t[g]||r(t,g,{});var o=t[g],a=function(r){var i=r.col,a=r.row,l="".concat(i,":").concat(a),s="".concat(l,"::block"),c="".concat(l,"::elapsed");if(!n.disabled&&!o[s]){var f=t.getHeaderValue(i,a);t.setHeaderValue(i,a,!f),u(200,(function(n){1===n?delete o[c]:o[c]=n,t.invalidateGridRect(e.startCol,e.startRow,e.endCol,e.endRow)}))}};return[].concat(f(i(t,e,{action:a,mouseOver:function(e){return!n.disabled&&(o.mouseActiveCell={col:e.col,row:e.row},t.invalidateCell(e.col,e.row),!0)},mouseOut:function(e){delete o.mouseActiveCell,t.invalidateCell(e.col,e.row)}})),f(l(t,e,{action:a,acceptKeys:[13,32]})))}}]),e}(d);t.exports=p},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._textAlign=n.textAlign||"left",t._textBaseline=n.textBaseline||"middle",t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"textAlign",get:function(){return this._textAlign},set:function(t){this._textAlign=t,this.doChangeStyle()}},{key:"textBaseline",get:function(){return this._textBaseline},set:function(t){this._textBaseline=t,this.doChangeStyle()}}]),e}(n(55));t.exports=o},function(t,e,n){var r,o=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._sortArrowColor=n.sortArrowColor,t}return c(e,t),v(e,null,[{key:"DEFAULT",get:function(){return r||(r=new e)}}]),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"sortArrowColor",get:function(){return this._sortArrowColor},set:function(t){this._sortArrowColor=t,this.doChangeStyle()}}]),e}(n(27));t.exports=o},function(t,e,n){var r=n(12).Style,o=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"drawInternal",value:function(t,e,n,r,o,i){var a=i.drawCellBase,l=n.textAlign,s=n.textBaseline,c=n.color,u=n.font,f=n.bgColor,h=n.textOverflow;f&&a({bgColor:f}),r.text(t,e,{textAlign:l,textBaseline:s,color:c,font:u,textOverflow:h})}},{key:"bindGridEvent",value:function(t,e){return[]}},{key:"StyleClass",get:function(){return r}}]),e}(n(18));t.exports=o},function(t,e,n){var r=n(12).SortHeaderStyle,o=n(18),i=n(0).isDef,l=n(8).getFontSize,u=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"drawInternal",value:function(t,e,n,r,o,a){var s=a.drawCellBase,c=n.textAlign,u=n.textBaseline,f=void 0===u?"middle":u,h=n.color,d=n.bgColor,v=n.font,g=n.textOverflow,p=n.sortArrowColor;d&&s({bgColor:d});var y=o.sortState,_=void 0,m=e.col,w=e.row,C=o.getHeaderCellRange(m,w);C.isCellInRange(y.col,C.startRow)&&(_=y.order);var x=e.getContext(),b=1.2*l(x,v).width;r.text(t,e,{textAlign:c,textBaseline:f,color:h,font:v,textOverflow:g,icons:[{name:i(_)?"asc"===_?"arrow_downward":"arrow_upward":null,width:b,color:r.getColor(p||r.theme.header.sortArrowColor,m,w,x)||"rgba(0, 0, 0, 0.38)"}]})}},{key:"StyleClass",get:function(){return r}}]),e}(o);t.exports=u},function(t,e,n){var r=n(0).isDef,o=n(18),i=n(56),l=n(1).CHECK_HEADER_STATE_ID,u=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"clone",value:function(){return new e(this)}},{key:"drawInternal",value:function(t,e,n,o,i,a){var s=a.drawCellBase,c=n.textAlign,u=n.textBaseline,f=n.borderColor,h=n.checkBgColor,d=n.uncheckBgColor,v=n.bgColor,g=n.color,p=n.font,y=n.textOverflow;v&&s({bgColor:v});var _=e.col,m=e.row,w="".concat(_,":").concat(m),C="".concat(w,"::elapsed"),x=(i[l]||{})[C],b=i.getHeaderValue(_,m),k={textAlign:c,textBaseline:u,borderColor:f,checkBgColor:h,uncheckBgColor:d};r(x)&&(k.animElapsedTime=x);var E=o.buildCheckBoxInline(!!b,e,k);o.text([E,t],e,{textAlign:c,textBaseline:u,color:g,font:p,textOverflow:y})}},{key:"bindGridEvent",value:function(t,e,n){return[]}},{key:"StyleClass",get:function(){return i}}]),e}(o);t.exports=u},function(t,e,n){var r=n(0).getChainSafe,o=n(1).get();function i(t,e,n,o){return r.apply(void 0,[t].concat(f(n)))||r.apply(void 0,[e].concat(f(n)))||o&&r.apply(void 0,[t].concat(f(o)))||o&&r.apply(void 0,[e].concat(f(o)))}var a=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};h(this,t),e.hiliteBorderColor&&!e.highlightBorderColor&&(console.warn("Please use highlightBorderColor instead of hiliteBorderColor. cheetah-grid@>=0.7"),e.highlightBorderColor=e.hiliteBorderColor),this[o]={obj:e,superTheme:n}}return v(t,[{key:"extends",value:function(e){return new t(e,this)}},{key:"font",get:function(){var t=this[o];return i(t.obj,t.superTheme,["font"])}},{key:"underlayBackgroundColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["underlayBackgroundColor"])}},{key:"color",get:function(){var t=this[o];return i(t.obj,t.superTheme,["color"])}},{key:"frozenRowsColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["frozenRowsColor"],["color"])}},{key:"defaultBgColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["defaultBgColor"])}},{key:"frozenRowsBgColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["frozenRowsBgColor"],["defaultBgColor"])}},{key:"selectionBgColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["selectionBgColor"],["defaultBgColor"])}},{key:"borderColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["borderColor"])}},{key:"frozenRowsBorderColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["frozenRowsBorderColor"],["borderColor"])}},{key:"highlightBorderColor",get:function(){var t=this[o];return i(t.obj,t.superTheme,["highlightBorderColor"],["borderColor"])}},{key:"checkbox",get:function(){var t=this[o],e=t.obj,n=t.superTheme;return this._checkbox||(this._checkbox={get uncheckBgColor(){return i(e,n,["checkbox","uncheckBgColor"],["defaultBgColor"])},get checkBgColor(){return i(e,n,["checkbox","checkBgColor"],["defaultBgColor"])},get borderColor(){return i(e,n,["checkbox","borderColor"],["borderColor"])}})}},{key:"button",get:function(){var t=this[o],e=t.obj,n=t.superTheme;return this._button||(this._button={get color(){return i(e,n,["button","color"],["color"])},get bgColor(){return i(e,n,["button","bgColor"],["defaultBgColor"])}})}},{key:"header",get:function(){var t=this[o],e=t.obj,n=t.superTheme;return this._header||(this._header={get sortArrowColor(){return i(e,n,["header","sortArrowColor"],["color"])}})}}]),t}();t.exports={Theme:a}},function(t,e,n){var r={};t.exports={color:"#000",defaultBgColor:function(t){return t.row-t.grid.frozenRowCount&1?"#F6F6F6":"#FFF"},frozenRowsBgColor:function(t){var e=t.col,n=t.grid,o=t.grid.frozenRowCount,i=t.context,a=n.getCellRelativeRect(e,0),l=a.left;return function(t,e,n,o,i,a){var l,s=[];for(l in a)s.push("".concat(l,"@").concat(a[l]));var c="".concat(e,"/").concat(n,"/").concat(o,"/").concat(i,"/").concat(s.join(",")),u=r[c];if(u)return u;var f=t.createLinearGradient(e,n,e,i);for(l in a)f.addColorStop(l,a[l]);return r[c]=f}(i,l,a.top,l,n.getCellRelativeRect(e,o-1).bottom,{0:"#FFF",1:"#D3D3D3"})},selectionBgColor:"#CCE0FF",borderColor:"#000",highlightBorderColor:"#5E9ED6",checkbox:{uncheckBgColor:"#FFF",checkBgColor:"rgb(76, 73, 72)"},button:{color:"#FFF",bgColor:"#2196F3"},header:{sortArrowColor:"rgba(0, 0, 0, 0.38)"},underlayBackgroundColor:"#F6F6F6"}},function(t,e,n){t.exports={color:"rgba(0, 0, 0, 0.87)",frozenRowsColor:"rgba(0, 0, 0, 0.54)",defaultBgColor:"#FFF",selectionBgColor:"#CCE0FF",borderColor:function(t){var e=t.col,n=t.grid,r=n.colCount;return n.frozenColCount-1===e?["#ccc7c7","#f2f2f2","#ccc7c7",null]:r-1===e?["#ccc7c7","#f2f2f2","#ccc7c7",null]:["#ccc7c7",null]},frozenRowsBorderColor:function(t){var e=t.row;return t.grid.frozenRowCount-1===e?["#f2f2f2","#f2f2f2","#ccc7c7","#f2f2f2"]:["#f2f2f2"]},highlightBorderColor:"#5E9ED6",checkbox:{checkBgColor:"rgb(76, 73, 72)",borderColor:"rgba(0, 0, 0, 0.26)"},button:{color:"#FFF",bgColor:"#2196F3"},header:{sortArrowColor:"rgba(0, 0, 0, 0.38)"},underlayBackgroundColor:"#FFF"}},function(t,e,r){var o=r(29),i=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return h(this,e),(t=a(this,s(e).call(this,n)))._rCache={},t._fCache={},t}return c(e,t),v(e,null,[{key:"ofArray",value:function(t){return new e({get:function(e){return t[e]},length:t.length})}},{key:"EVENT_TYPE",get:function(){return o.EVENT_TYPE}}]),v(e,[{key:"getOriginal",value:function(t){return this._rCache&&this._rCache[t]?this._rCache[t]:n(s(e.prototype),"getOriginal",this).call(this,t)}},{key:"getOriginalField",value:function(t,r){var o=this._fCache&&this._fCache[t];if(o){var i=o.get(r);if(i)return i}return n(s(e.prototype),"getOriginalField",this).call(this,t,r)}},{key:"setOriginalField",value:function(t,r,o){var i=this._fCache;return i&&i[t]&&delete i[t],n(s(e.prototype),"setOriginalField",this).call(this,t,r,o)}},{key:"clearCache",value:function(){this._rCache&&(this._rCache={}),this._fCache&&(this._fCache={})}},{key:"fieldPromiseCallBackInternal",value:function(t,e,n){!function(t,e,n,r){var o=t._fCache;(o[e]||(o[e]=new Map)).set(n,r)}(this,t,e,n)}},{key:"recordPromiseCallBackInternal",value:function(t,e){this._rCache[t]=e}},{key:"dispose",value:function(){n(s(e.prototype),"dispose",this).call(this)}}]),e}(o);t.exports=i},function(e,r,o){var i=o(0).isPromise,l=o(29),u=o(3),f=l.EVENT_TYPE,d=function(){function t(e){h(this,t),this._dataSource=e,this._curIndex=-1,this._data=[]}return v(t,[{key:"hasNext",value:function(){var t=this._curIndex+1;return this._dataSource.length>t}},{key:"next",value:function(){var t=this._curIndex+1,e=this._getIndexData(t);return this._curIndex=t,e}},{key:"movePrev",value:function(){this._curIndex--}},{key:"_getIndexData",value:function(t,e){var n=this._dataSource,r=this._data;if(t<r.length)return r[t];if(!(n.length<=t)){var o=this._dataSource.get(t);if(r[t]=o,i(o)&&(o.then((function(e){r[t]=e})),!e))for(var a=1;a<=100;a++)this._getIndexData(t+a,!0);return o}}}]),t}(),g=function(){function e(t,n,r){h(this,e),this._owner=t,this._dataSourceItr=new d(n),this._filter=r,this._filterdList=[],this._queues=[]}return v(e,[{key:"get",value:function(t){if(!this._cancel){var e=this._filterdList;if(t<e.length)return e[t];var n=this._queues;return n[t]?n[t]:n[t]||this._findIndex(t)}}},{key:"cancel",value:function(){this._cancel=!0}},{key:"_findIndex",value:function(e){if(t.Promise){var n=Date.now()+100,r=0;return this._findIndexWithTimeout(e,(function(){return++r>=100&&(r=0,n<Date.now())}))}return this._findIndexWithTimeout(e,(function(){return!1}))}},{key:"_findIndexWithTimeout",value:function(t,e){for(var n=this,r=this._filterdList,o=this._filter,a=this._dataSourceItr,l=this._queues;a.hasNext();){if(this._cancel)return;var s=a.next();if(i(s))return a.movePrev(),l[t]=s.then((function(e){return l[t]=null,n.get(t)}));if(o(s)&&(r.push(s),t<r.length))return r[t];if(e()){var c=new Promise((function(t){setTimeout((function(){t()}),300)}));return l[t]=c.then((function(){return l[t]=null,n.get(t)})),l[t]}}this._owner.length=r.length}}]),e}(),p=function(t){function e(t,n){var r;h(this,e),(r=a(this,s(e).call(this,t)))._dataSource=t,r.filter=n;var o=r._handler=new u;o.on(t,f.UPDATED_ORDER,(function(){r.filter=r.filter}));var i=function(e){var n=f[e];o.on(t,n,(function(){for(var t,e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];return(t=r).fireListeners.apply(t,[n].concat(o))}))};for(var l in f)i(l);return r}return c(e,t),v(e,null,[{key:"EVENT_TYPE",get:function(){return f}}]),v(e,[{key:"getOriginal",value:function(t){return this._filterData?this._filterData.get(t):n(s(e.prototype),"getOriginal",this).call(this,t)}},{key:"sort",value:function(){var t;return(t=this._dataSource).sort.apply(t,arguments)}},{key:"dispose",value:function(){this._handler.dispose(),n(s(e.prototype),"dispose",this).call(this)}},{key:"filter",get:function(){return this._filterData&&this._filterData._filter||void 0},set:function(t){this._filterData&&this._filterData.cancel(),this._filterData=t?new g(this,this._dataSource,t):void 0,this.length=this._dataSource.length}}]),e}(l);e.exports=p},function(r,o,i){(function(o){var u=i(0),d=u.isDef,g=u.isPromise,p=u.then,y=u.obj.isObject,_=i(60),m=i(37),w=m.style.BaseStyle,C=i(54),x=i(52),b=i(12).BaseStyle,k=i(5),E=i(58),I=E.DataSource,L=E.CachedDataSource,S=i(28),T=i(25),A=i(134),R=i(146),O=i(31),B=i(1).PROTECTED_SYMBOL,M=0;function D(t,e,n){return t[B].headerMap.getHeaderCellRange(e,n)}function P(t,e,n){if(n<t[B].headerMap.rowCount)return t[B].headerMap.getCell(e,n).caption;var r=t[B].headerMap.columns[e].field;return y(r)&&r.get&&r.set&&(r=r.get),W(t,r,n)}function F(t,e,n){if(n<t[B].headerMap.rowCount)return null;var r=t[B].headerMap.columns[e].message;return r?W(t,r,n):null}function z(t,e,n){if(!d(e))return null;if(o(e))return e.map((function(e){return z(t,e,n)}));if(!y(e)||"function"==typeof e)return W(t,e,n);var r={};return T.iconPropKeys.forEach((function(o){if(e[o]){var i=W(t,e[o],n);d(i)?r[o]=i:function(t,e,n){if(!d(e))return!1;if(n<t[B].headerMap.rowCount)return!1;var r=K(t,n);return t[B].dataSource.hasField(r,e)}(t,e[o],n)||(r[o]=e[o])}})),r}function W(t,e,n){if(!d(e))return null;if(n<t[B].headerMap.rowCount)return null;var r=K(t,n);return t[B].dataSource.getField(r,e)}function H(t,e,n,r,o,i){var a=r.col,l=r.row,s=t[B].gridCanvasHelper,c=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.bgColor,r={fillColor:e};s.fillCellWithState(n,r)},u=function(){if(n.col===t.frozenColCount-1){var e=n.getRect();s.drawWithClip(n,(function(r){var o=n.row>=t.frozenRowCount?s.theme.borderColor:s.theme.frozenRowsBorderColor,i=s.toBoxArray(s.getColor(o,n.col,n.row,r));i[1]&&(r.lineWidth=1,r.strokeStyle=i[1],r.beginPath(),r.moveTo(e.right-2.5,e.top),r.lineTo(e.right-2.5,e.bottom),r.stroke())}))}n.row<t[B].headerMap.rowCount?function(t,e,n){var r=n.col,o=n.row,i=t.selection.select;if(i.row>=t[B].headerMap.rowCount)e.borderWithState(n);else{var a=t[B].headerMap.getCell(r,o).id,l=n.getRect(),s={},c=t[B].headerMap.getCell(i.col,i.row).id;if(c===a)s.borderColor=e.theme.highlightBorderColor,s.lineWidth=2,e.border(n,s);else{s.lineWidth=1;var u=t.isFrozenCell(r,o);u&&u.row&&(s.borderColor=e.theme.frozenRowsBorderColor),e.border(n,s),r>0&&t[B].headerMap.getCell(r-1,o).id===c?e.drawBorderWithClip(n,(function(t){var n=e.toBoxArray(e.getColor(e.theme.highlightBorderColor,i.col,i.row,t));n[1]&&(t.lineWidth=1,t.strokeStyle=n[1],t.beginPath(),t.moveTo(l.left-.5,l.top),t.lineTo(l.left-.5,l.bottom),t.stroke())})):o>0&&t[B].headerMap.getCell(r,o-1).id===c&&e.drawBorderWithClip(n,(function(t){var n=e.toBoxArray(e.getColor(e.theme.highlightBorderColor,i.col,i.row,t));n[0]&&(t.lineWidth=1,t.strokeStyle=n[0],t.beginPath(),t.moveTo(l.left,l.top-.5),t.lineTo(l.right,l.top-.5),t.stroke())}))}}}(t,s,n):s.borderWithState(n)};return i(e,{getRecord:function(){return t.getRowRecord(l)},getIcon:function(){return function(t,e,n){return z(t,t[B].headerMap.columns[e].icon,n)}(t,a,l)},getMessage:function(){return F(t,a,l)},messageHandler:t[B].messageHandler,style:o,drawCellBase:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.bgColor;c({bgColor:e}),u()},drawCellBg:c,drawCellBorder:u},n,t)}function N(t){t[B].headerEvents&&t[B].headerEvents.forEach((function(e){return t.unlisten(e)})),t[B].headerMap=new Y(t[B].header),t[B].headerEvents=[],t[B].headerMap.headerObjects.forEach((function(e){var n,r=e.headerType.bindGridEvent(t,e.range);if((n=t[B].headerEvents).push.apply(n,f(r)),e.style&&e.style instanceof b){var o=e.style.listen(b.EVENT_TYPE.CHANGE_STYLE,(function(){t.invalidate()}));t[B].headerEvents.push(o)}if(e.action){var i,a=e.action.bindGridEvent(t,e.range);(i=t[B].headerEvents).push.apply(i,f(a))}})),t[B].headerMap.columns.forEach((function(e,n){if(e.action){var r,o=e.action.bindGridEvent(t,n,{isTarget:function(e,r){return n===e&&t.frozenRowCount<=r}});(r=t[B].headerEvents).push.apply(r,f(o))}if(e.columnType){var i,a=e.columnType.bindGridEvent(t,n,{isTarget:function(e,r){return n===e&&t.frozenRowCount<=r}});(i=t[B].headerEvents).push.apply(i,f(a))}if(e.style&&e.style instanceof w){var l=e.style.listen(m.style.EVENT_TYPE.CHANGE_STYLE,(function(){t.invalidate()}));t[B].headerEvents.push(l)}}));for(var e=0;e<t[B].headerMap.columns.length;e++){var n=t[B].headerMap.columns[e],r=n.width,i=n.minWidth,a=n.maxWidth;r&&(r>0||"string"==typeof r)&&t.setColWidth(e,r),i&&(i>0||"string"==typeof i)&&t.setMinColWidth(e,i),a&&(a>0||"string"==typeof a)&&t.setMaxColWidth(e,a)}for(var l=o(t[B].headerRowHeight),s=0;s<t[B].headerMap.rowCount;s++){var c=l?t[B].headerRowHeight[s]:t[B].headerRowHeight;c&&c>0&&t.setRowHeight(s,c)}t.colCount=t[B].headerMap.columns.length,U(t),t.frozenRowCount=t[B].headerMap.rowCount}function U(t){t.rowCount=t[B].dataSource.length+t[B].headerMap.rowCount}function V(t,e){t[B].dataSourceEventIds&&t[B].dataSourceEventIds.forEach((function(e){return t[B].handler.off(e)})),e(t),t[B].dataSourceEventIds=[t[B].handler.on(t[B].dataSource,I.EVENT_TYPE.UPDATED_LENGTH,(function(){U(t),t.invalidate()})),t[B].handler.on(t[B].dataSource,I.EVENT_TYPE.UPDATED_ORDER,(function(){t.invalidate()}))]}function j(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];V(t,(function(){t[B].records=e,t[B].dataSource=L.ofArray(t[B].records)}))}function G(t,e){V(t,(function(){t[B].dataSource=e?e instanceof I?e:new L(e):I.EMPTY,t[B].records=null}))}function K(t,e){return e<t[B].headerMap.rowCount?void 0:e-t[B].headerMap.rowCount}var Y=function(){function t(e){h(this,t),this._columns=[],this._headerCells=[],this._headerObjects=[],this._addHeaders(0,e,[]),this._setupHeaderControllers()}return v(t,[{key:"getCell",value:function(t,e){return this._headerCells[e][t]}},{key:"getHeaderCellRangeById",value:function(t){for(var e=0;e<this.rowCount;e++)for(var n=0;n<this.columns.length;n++)if(t===this.getCell(n,e).id)return this.getHeaderCellRange(n,e)}},{key:"getHeaderCellRange",value:function(t,e){for(var n={startCol:t,startRow:e,endCol:t,endRow:e,isCellInRange:function(t,e){return this.startCol<=t&&t<=this.endCol&&this.startRow<=e&&e<=this.endRow}},r=this.getCell(t,e).id,o=t-1;o>=0&&r===this.getCell(o,e).id;o--)n.startCol=o;for(var i=t+1;i<this.columns.length&&r===this.getCell(i,e).id;i++)n.endCol=i;for(var a=e-1;a>=0&&r===this.getCell(t,a).id;a--)n.startRow=a;for(var l=e+1;l<this.rowCount&&r===this.getCell(t,l).id;l++)n.endRow=l;return n}},{key:"_addHeaders",value:function(t,e,n){var r=this,o=this._headerCells[t]||this._newRow(t);e.forEach((function(e){var i=r._columns.length,a={id:M++,caption:e.caption,field:e.headerField||e.field,style:e.headerStyle,headerType:e.headerType,action:e.headerAction,sort:e.sort,define:e};r._headerObjects.push(a),o[i]=a;for(var l=t-1;l>=0;l--)r._headerCells[l][i]=n[l];if(e.columns)r._addHeaders(t+1,e.columns,[].concat(f(n),[a]));else{r._columns.push({field:e.field,width:e.width,minWidth:e.minWidth,maxWidth:e.maxWidth,icon:e.icon,message:e.message,columnType:m.type.of(e.columnType),action:m.action.of(e.action),style:e.style,define:e});for(var s=t+1;s<r._headerCells.length;s++)r._headerCells[s][i]=a}}))}},{key:"_setupHeaderControllers",value:function(){var t=this;this._headerObjects.forEach((function(e){e.range=t.getHeaderCellRangeById(e.id),e.headerType=C.ofCell(e),e.action=x.ofCell(e)}))}},{key:"_newRow",value:function(t){var e=this._headerCells[t]=[];if(!this._columns.length)return e;for(var n=this._headerCells[t-1],r=0;r<n.length;r++)e[r]=n[r];return e}},{key:"columns",get:function(){return this._columns}},{key:"rowCount",get:function(){return this._headerCells.length}},{key:"headerObjects",get:function(){return this._headerObjects}}]),t}(),q=function(r){function o(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,o);var r=(e=a(this,s(o).call(this,function(t){return t&&(delete t.frozenRowCount,delete t.colCount,delete t.rowCount),t}(n))))[B];return r.header=n.header||[],r.headerRowHeight=n.headerRowHeight||[],n.dataSource?G(l(e),n.dataSource):j(l(e),n.records),N(l(e)),r.sortState={col:-1,row:-1,order:void 0},r.gridCanvasHelper=new _(l(e)),r.theme=S.of(n.theme),r.messageHandler=new A(l(e),(function(t,n){return F(l(e),t,n)})),r.tooltipHandler=new R(l(e)),e.invalidate(),r.handler.on(t,"resize",(function(){e.updateSize(),e.updateScroll(),e.invalidate()})),e}return c(o,r),v(o,null,[{key:"EVENT_TYPE",get:function(){return O}}]),v(o,[{key:"dispose",value:function(){var t=this[B];t.messageHandler.dispose(),t.tooltipHandler.dispose(),n(s(o.prototype),"dispose",this).call(this)}},{key:"getField",value:function(t){return this[B].headerMap.columns[t].field}},{key:"getColumnDefine",value:function(t){return this[B].headerMap.columns[t].define}},{key:"getHeaderField",value:function(t,e){return this[B].headerMap.getCell(t,e).field}},{key:"getHeaderDefine",value:function(t,e){return this[B].headerMap.getCell(t,e).define}},{key:"getRowRecord",value:function(t){return t<this[B].headerMap.rowCount?void 0:this[B].dataSource.get(K(this,t))}},{key:"getColumnIndexByField",value:function(t){for(var e in this[B].headerMap.columns)if(this[B].headerMap.columns[e].field===t)return e-0;return null}},{key:"focusGridCell",value:function(t,e){var n=this.selection.range,r=n.start,o=r.col,i=r.row,a=n.end,l=a.col,s=a.row,c=this.getColumnIndexByField(t),u=e+this[B].headerMap.rowCount;this.focusCell(c,u),this.selection.select={col:c,row:u},this.invalidateGridRect(o,i,l,s),this.invalidateCell(c,u)}},{key:"makeVisibleGridCell",value:function(t,e){this.makeVisibleCell(this.getColumnIndexByField(t),e+this[B].headerMap.rowCount)}},{key:"getGridCanvasHelper",value:function(){return this[B].gridCanvasHelper}},{key:"getHeaderCellRange",value:function(t,e){return D(this,t,e)}},{key:"getCopyCellValue",value:function(t,e){if(e<this[B].headerMap.rowCount){var n=D(this,t,e),r=n.startCol,o=n.startRow;if(r!==t||o!==e)return""}return P(this,t,e)}},{key:"onDrawCell",value:function(t,e,n){var r,o,i=this[B].headerMap,a=i.columns[t];if(e<i.rowCount){var l=i.getCell(t,e);r=l.headerType.onDrawCell,o=l.style,function(t,e,n,r){for(var o=r.getRect(),i=D(t,e,n),a=i.startCol,l=i.endCol,s=i.startRow,c=i.endRow,u=e-1;u>=a;u--)o.left-=t.getColWidth(u);for(var f=e+1;f<=l;f++)o.right+=t.getColWidth(f);for(var h=n-1;h>=s;h--)o.top-=t.getRowHeight(h);for(var d=n+1;d<=c;d++)o.bottom+=t.getRowHeight(d);r.setRect(o)}(this,t,e,n)}else r=a.columnType.onDrawCell,o=a.style;return H(this,P(this,t,e),n,{col:t,row:e},o,r)}},{key:"doGetCellValue",value:function(t,e,n){if(e<this[B].headerMap.rowCount)return!1;var r=P(this,t,e);return!g(r)&&(n(r),!0)}},{key:"doChangeValue",value:function(t,e,n){var r=this;if(e<this[B].headerMap.rowCount)return!1;var o=P(this,t,e);if(g(o))return!1;var i=n(o);return void 0!==i&&p(function(t,e,n,r){if(n<t[B].headerMap.rowCount)return!1;var o=t[B].headerMap.columns[e].field;y(o)&&o.get&&o.set&&(o=o.set);var i=K(t,n),a=t[B].dataSource.setField(i,o,r);return!g(a)||a}(this,t,e,i),(function(n){if(n){var a=r[B].headerMap.columns[t].field,l=r;r.fireListeners(O.CHANGED_VALUE,{col:t,row:e,get record(){return l.getRowRecord(e)},field:a,value:i,oldValue:o})}return n}))}},{key:"getHeaderValue",value:function(t,e){var n=this.getHeaderField(t,e);return this.headerValues[n]}},{key:"setHeaderValue",value:function(t,e,n){var r=this.getHeaderField(t,e),o=this.headerValues[r];this.headerValues[r]=n,this.fireListeners(O.CHANGED_HEADER_VALUE,{col:t,row:e,field:r,value:n,oldValue:o})}},{key:"bindEventsInternal",value:function(){var t=this;this.listen(O.SELECTED_CELL,(function(e){if(e.row<t[B].headerMap.rowCount&&e.col<t[B].headerMap.columns.length){var n=D(t,e.col,e.row),r=n.startCol,o=n.endCol,i=n.startRow,a=n.endRow;r===o&&i===a||t.invalidateGridRect(r,i,o,a)}}))}},{key:"getMoveLeftColByKeyDownInternal",value:function(t){var e=t.col,r=t.row;return r<this[B].headerMap.rowCount&&(e=D(this,e,r).startCol),n(s(o.prototype),"getMoveLeftColByKeyDownInternal",this).call(this,{col:e,row:r})}},{key:"getMoveRightColByKeyDownInternal",value:function(t){var e=t.col,r=t.row;return r<this[B].headerMap.rowCount&&(e=D(this,e,r).endCol),n(s(o.prototype),"getMoveRightColByKeyDownInternal",this).call(this,{col:e,row:r})}},{key:"getMoveUpRowByKeyDownInternal",value:function(t){var e=t.col,r=t.row;return r<this[B].headerMap.rowCount&&(r=D(this,e,r).startRow),n(s(o.prototype),"getMoveUpRowByKeyDownInternal",this).call(this,{col:e,row:r})}},{key:"getMoveDownRowByKeyDownInternal",value:function(t){var e=t.col,r=t.row;return r<this[B].headerMap.rowCount&&(r=D(this,e,r).endRow),n(s(o.prototype),"getMoveDownRowByKeyDownInternal",this).call(this,{col:e,row:r})}},{key:"getOffsetInvalidateCells",value:function(){return 1}},{key:"header",get:function(){return this[B].header},set:function(t){this[B].header=t,N(this)}},{key:"records",get:function(){return this[B].records},set:function(t){j(this,t),U(this),this.invalidate()}},{key:"dataSource",get:function(){return this[B].dataSource},set:function(t){G(this,t),U(this),this.invalidate()}},{key:"theme",get:function(){return this[B].theme},set:function(t){this[B].theme=S.of(t),this.invalidate()}},{key:"font",get:function(){return n(s(o.prototype),"font",this)||this[B].gridCanvasHelper.theme.font},set:function(t){e(s(o.prototype),"font",t,this,!0)}},{key:"underlayBackgroundColor",get:function(){return n(s(o.prototype),"underlayBackgroundColor",this)||this[B].gridCanvasHelper.theme.underlayBackgroundColor},set:function(t){e(s(o.prototype),"underlayBackgroundColor",t,this,!0)}},{key:"sortState",get:function(){return this[B].sortState},set:function(t){var e,n=this.sortState;n.col>=0&&n.row>=0&&(e=this.getHeaderField(n.col,n.row));var r,o=this[B].sortState=d(t)?t:{col:-1,row:-1,order:void 0};o.col>=0&&o.row>=0&&(r=this.getHeaderField(o.col,o.row)),d(e)&&e!==r&&this.setHeaderValue(n.col,n.row,void 0),d(r)&&this.setHeaderValue(o.col,o.row,o.order)}},{key:"headerValues",get:function(){return this[B].headerValues||(this[B].headerValues={})},set:function(t){this[B].headerValues=t||{}}}]),o}(k);r.exports=q}).call(this,i(2).Array_isArray)},function(t,e,n){(function(e){var r=n(13),o=n(119),i=n(62),a=n(122),l=n(63),s=n(123),c=n(0).isDef,u=n(8).calcStartPosition,h=n(64),d=n(30);t.exports={iconOf:function(t){if(t instanceof r)return t;if(!t)return null;if(t.font&&t.content)return new o(t);if(t.src)return new i({src:t.src,width:t.width,height:t.width});if(t.svg)return new a({svg:t.svg,width:t.width,height:t.width});if(t.path)return new s({path:t.path,width:t.width,height:t.width,color:t.color});var e=h.get();if(t.name&&e[t.name]){var n=e[t.name],c=t.width||Math.max(n.width,n.height);return new l({draw:function(t){var e=t.ctx,r=(t.canvashelper,t.rect),o=t.offset,i=t.offsetLeft,a=t.offsetRight,l=t.offsetTop,s=t.offsetBottom;!function(t,e,n,r,o,i,a,l){var s=arguments.length>8&&void 0!==arguments[8]?arguments[8]:{},c=s.offset,f=void 0===c?2:c,h=s.padding,v={left:o,top:i,width:a,height:l,right:o+a,bottom:i+l};t.save();try{t.beginPath(),t.rect(v.left,v.top,v.width,v.height),t.clip();var g=u(t,v,n,r,{offset:f,padding:h});d.fill(e,t,g.x,g.y,n,r)}finally{t.restore()}}(e,n,c,c,r.left,r.top,r.width,r.height,{offset:o+1,padding:{left:i,right:a,top:l,bottom:s}})},width:c,height:c,color:t.color})}return new o(t)},of:function(t){return c(t)?t instanceof r?t:new r(t):null},buildInlines:function(t,n){var o=this,i=[];if(t&&i.push.apply(i,f(t.map((function(t){return o.iconOf(t)})).filter((function(t){return!!t})))),e(n)&&n.filter((function(t){return t instanceof r})).length)i.push.apply(i,f(n.map((function(t){return o.of(t)})).filter((function(t){return!!t}))));else{var a=this.of(n);a&&i.push(a)}return i},string:function(t){return this.buildInlines(void 0,t).join("")}}}).call(this,n(2).Array_isArray)},function(t,e,n){var r=n(13),o=n(61),i=function(t){function e(t){var n;return h(this,e),(n=a(this,s(e).call(this)))._icon=t||{},n}return c(e,t),v(e,[{key:"width",value:function(t){var e=t.ctx,n=this._icon;if(n.width)return n.width;if(n.font&&o.check(n.font,n.content)){e.save();try{return e.font=n.font||e.font,e.measureText(n.content).width}finally{e.restore()}}return null}},{key:"font",value:function(){return this._icon.font}},{key:"color",value:function(){return this._icon.color}},{key:"canDraw",value:function(){var t=this._icon;return!t.font||o.check(t.font,t.content)}},{key:"onReady",value:function(t){var e=this._icon;e.font&&!o.check(e.font,e.content)&&o.load(e.font,e.content,t)}},{key:"draw",value:function(t){var e=t.ctx,n=t.canvashelper,r=t.rect,o=t.offset,i=t.offsetLeft,a=t.offsetRight,l=t.offsetTop,s=t.offsetBottom,c=this._icon;c.content&&n.fillTextRect(e,c.content,r.left,r.top,r.width,r.height,{offset:o+1,padding:{left:i,right:a,top:l,bottom:s}})}},{key:"canBreak",value:function(){return!1}},{key:"toString",value:function(){return""}}]),e}(r);t.exports=i},function(e,n,r){var o=r(121),i={SERIF:"serif",SANS_SERIF:"sans-serif"},a={},l=function(){function e(t,n){h(this,e),this.activeCallbacks=[],this.inactiveCallbacks=[],this.status=null,this.lastResortWidths_={},this.fontRulerA_=new o("".concat(t,",").concat(i.SERIF),n),this.fontRulerB_=new o("".concat(t,",").concat(i.SANS_SERIF),n);var r=new o("4px ".concat(i.SERIF),n),a=new o("4px ".concat(i.SANS_SERIF),n);this.lastResortWidths_[i.SERIF]=r.getWidth(),this.lastResortWidths_[i.SANS_SERIF]=a.getWidth(),r.remove(),a.remove(),this.started_=Date.now(),this.check_()}return v(e,null,[{key:"load",value:function(t,n,r,o){var i=a[t]||(a[t]={});(i[n+=""]?i[n]:i[n]=new e(t,n)).then(r,o)}}]),v(e,[{key:"then",value:function(t,e){this.status?"ng"!==this.status?t():e():(this.activeCallbacks.push(t),this.inactiveCallbacks.push(e))}},{key:"check_",value:function(){var t=this,e=this.fontRulerA_.getWidth(),n=this.fontRulerB_.getWidth();this.isFallbackFont_(e,n)||this.isLastResortFont_(e,n)?Date.now()-this.started_>=3e3?this.isLastResortFont_(e,n)?(this.finish_(this.activeCallbacks),this.status="ok"):(this.finish_(this.inactiveCallbacks),this.status="ng"):setTimeout((function(){t.check_()}),50):(this.finish_(this.activeCallbacks),this.status="ok")}},{key:"isFallbackFont_",value:function(t,e){return this.widthMatches_(t,i.SERIF)&&this.widthMatches_(e,i.SANS_SERIF)}},{key:"widthsMatchLastResortWidths_",value:function(t,e){for(var n in i)if(i.hasOwnProperty(n)&&this.widthMatches_(t,i[n])&&this.widthMatches_(e,i[n]))return!0;return!1}},{key:"widthMatches_",value:function(t,e){return t===this.lastResortWidths_[e]}},{key:"isLastResortFont_",value:function(e,n){return function(){if(null===s){var e=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(t.navigator.userAgent);s=!!e&&(parseInt(e[1],10)<536||536===parseInt(e[1],10)&&parseInt(e[2],10)<=11)}return s}()&&this.widthsMatchLastResortWidths_(e,n)}},{key:"finish_",value:function(t){var e=this;setTimeout((function(){e.fontRulerA_.remove(),e.fontRulerB_.remove(),t.forEach((function(t){return t()}))}),0)}}]),e}(),s=null;e.exports=l},function(t,e,n){var r=function(){function t(e,n){h(this,t);var r=document.createElement("span");r.setAttribute("aria-hidden","true"),r.textContent=n||"BESbswy",function(t){return[{display:"block",position:"absolute",top:"-9999px",left:"-9999px",width:"auto",height:"auto",margin:"0",padding:"0","white-space":"nowrap",font:t},{"font-variant":"normal","font-size":"300px","font-style":"normal","font-weight":"400","line-height":"normal"}]}(e).forEach((function(t){for(var e in t)r.style[e]=t[e]})),document.body.appendChild(r),this.el_=r}return v(t,[{key:"getWidth",value:function(){return this.el_.offsetWidth}},{key:"remove",value:function(){document.body.removeChild(this.el_)}}]),t}();t.exports=r},function(t,e,n){var r=n(62),o=n(0).then;function i(t){var e="string"==typeof t?t:(new XMLSerializer).serializeToString(t);return"data:image/svg+xml;charset=utf-8,".concat(encodeURIComponent(e))}function l(t){return"string"==typeof t?(new DOMParser).parseFromString(t,"image/svg+xml"):t}var u=function(t){function e(t){var n=t.svg,r=t.width,c=t.height;t.imageLeft,t.imageTop,t.imageWidth,t.imageHeight,h(this,e);var u=o(n,l),f=u.getAttribute?u.getAttribute("width"):null,d=u.getAttribute?u.getAttribute("height"):null;return a(this,s(e).call(this,{src:o(n,i),width:r||f,height:c||d,imageWidth:f,imageHeight:d}))}return c(e,t),v(e,[{key:"canBreak",value:function(){return!1}},{key:"toString",value:function(){return""}}]),e}(r);t.exports=u},function(t,e,n){var r=n(13),o=n(8).calcStartPosition,i=n(30),l=function(t){function e(t){var n,r=t.path,o=t.width,l=t.height,c=t.color;return h(this,e),(n=a(this,s(e).call(this)))._path=new i.Path2D(r),n._width=o,n._height=l,n._color=c,n}return c(e,t),v(e,[{key:"width",value:function(t){return t.ctx,this._width}},{key:"font",value:function(){return null}},{key:"color",value:function(){return this._color}},{key:"canDraw",value:function(){return!0}},{key:"onReady",value:function(t){}},{key:"draw",value:function(t){var e=t.ctx,n=(t.canvashelper,t.rect),r=t.offset;r++;var i={left:t.offsetLeft,right:t.offsetRight,top:t.offsetTop,bottom:t.offsetBottom};e.save();try{e.beginPath(),e.rect(n.left,n.top,n.width,n.height),e.clip();var a=o(e,n,this._width,this._height,{offset:r,padding:i});e.translate(a.x,a.y),e.fill(this._path)}finally{e.restore()}}},{key:"canBreak",value:function(){return!1}},{key:"toString",value:function(){return""}}]),e}(r);t.exports=l},function(e,n,r){var o=new(r(125)),i=function t(e){var n=this;if(h(this,t),this._ops=[],["closePath","moveTo","lineTo","bezierCurveTo","quadraticCurveTo","arc","rect"].forEach((function(t){n[t]=function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];n._ops.push({op:t,args:r})}})),void 0!==e)if("string"==typeof e)this._ops=o.parse(e);else{if(!e.hasOwnProperty("_ops"))throw new Error("Error: ".concat(g(e)," is not a valid argument to Path"));this._ops=f(this._ops)}},a=t.CanvasRenderingContext2D,l=a.prototype.fill;a.prototype.fill=function(){for(var t=this,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];if(n[0]instanceof i){var o=n[0];this.beginPath(),o._ops.forEach((function(e){t[e.op].apply(t,f(e.args))})),l.apply(this,Array.prototype.slice.call(n,1))}else l.apply(this,n)},e.exports=i},function(e,n,r){var o=r(126);function i(t,e,n){return"M"===e.toUpperCase()||"L"===e.toUpperCase()||"T"===e.toUpperCase()?(t.command(e,n.next(),n.next()),"M"===e?"L":"m"===e?"l":e):"H"===e.toUpperCase()||"V"===e.toUpperCase()?(t.command(e,n.next()),e):"Z"===e.toUpperCase()?(t.command(e),e):"C"===e.toUpperCase()?(t.command(e,n.next(),n.next(),n.next(),n.next(),n.next(),n.next()),e):"S"===e.toUpperCase()||"Q"===e.toUpperCase()?(t.command(e,n.next(),n.next(),n.next(),n.next()),e):"A"===e.toUpperCase()?(t.command(e,n.next(),n.next(),n.next(),n.next(),n.next(),n.next(),n.next()),e):(console.warn("unsupported:".concat(e)),null)}var a=t.CanvasRenderingContext2D?Object.keys(t.CanvasRenderingContext2D.prototype):["save","restore","beginPath","closePath","moveTo","lineTo","bezierCurveTo","quadraticCurveTo","arc","arcTo","ellipse","rect","translate","rotate","scale"],l=function(){function t(){var e=this;h(this,t),this._commands=new o(this),a.forEach((function(t){e[t]=function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];e._ops.push({op:t,args:r})}}))}return v(t,[{key:"command",value:function(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];r=r||[];for(var i=0;i<r.length;i++)r[i]-=0;(e=this._commands)[t].apply(e,f(r))}},{key:"parse",value:function(t){var e=this,n=this._ops=[],r=function(t){var e=0;return{next:function(){for(var n="";t.length>e;){var r=t[e];if(e++," ,\n\r\t".indexOf(r)>-1){if(n)return n}else{if("str"==(".+-1234567890".indexOf(r)>-1?"num":"str"))return n?(e--,n):r;if("-+".indexOf(r)>-1&&n)return e--,n;if("."===r&&n.indexOf(".")>-1)return e--,n;n+=r}}return n||null}}}(t);try{!function(){for(var t,n;t=r.next();)isNaN(t-0)?n=i(e,t,r):function(){var o=!0;n=i(e,n,{next:function(){return o?(o=!1,t):r.next()}})}()}()}catch(e){throw console.log("Error: ".concat(t)),e}return n}}]),t}();e.exports=l},function(t,e,n){function r(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function o(t,e){var n,o,i,a=1;return t[0]*e[1]-t[1]*e[0]<0&&(a=-1),a*Math.acos((n=function(t,e){return function(t,e){return t[0]*e[0]+t[1]*e[1]}(t,e)/(r(t)*r(e))}(t,e),Math.min(Math.max(n,o),i)),-1,1)}function i(t,e,n,r,i,a,l,s,c,u){if(0!==r&&0!==i){a*=Math.PI/180,r=Math.abs(r),i=Math.abs(i);var f,h,d=function(t,e){var n=Math.cos(e),r=Math.sin(e);return[n*t[0]+r*t[1],-1*r*t[0]+n*t[1]]}((h=c,[((f=e)[0]-h[0])/2,(f[1]-h[1])/2]),a),v=function(t,e){return[t[0]*e[0],t[1]*e[1]]}(d,d),g=Math.pow(r,2),p=Math.pow(i,2),y=Math.sqrt(v[0]/g+v[1]/p);y>1&&(r*=y,i*=y,g=Math.pow(r,2),p=Math.pow(i,2));var _=Math.sqrt(Math.abs(g*p-g*v[1]-p*v[0])/(g*v[1]+p*v[0]));l===s&&(_*=-1);var m=function(t,e){return[t*e[0],t*e[1]]}(_,[r*d[1]/i,-i*d[0]/r]),w=function(t,e){return[t[0]+e[0],t[1]+e[1]]}(function(t,e){var n=Math.cos(e),r=Math.sin(e);return[n*t[0]-r*t[1],r*t[0]+n*t[1]]}(m,a),function(t,e){return[(t[0]+e[0])/2,(t[1]+e[1])/2]}(e,c)),C=[(d[0]-m[0])/r,(d[1]-m[1])/i],x=[(-1*d[0]-m[0])/r,(-1*d[1]-m[1])/i],b=o([1,0],C),k=b,E=b+o(C,x);t.save(),t.translate(w[0],w[1]),t.rotate(a),t.scale(r,i),t.arc(0,0,1,k,E,1-s),t.restore()}else t.lineTo(c,e)}t.exports=function t(e){var n,r,o=this;h(this,t);var a,l=0,s=0,c="";function u(){return"CcSsQqTt".indexOf(c)<0?{x:l,y:s}:a}this.M=function(t,i){return e.moveTo(t,i),n=t,r=i,l=t,s=i,c="M",o},this.m=function(t,e){return o.M(t+l,e+s)},this.L=function(t,n){return e.lineTo(t,n),l=t,s=n,c="L",o},this.l=function(t,e){return o.L(t+l,e+s)},this.H=function(t){return o.L(t,s)},this.h=function(t){return o.H(t+l)},this.V=function(t){return o.L(l,t)},this.v=function(t){return o.V(t+s)},this.Z=function(){return e.closePath(),l=n,s=r,c="Z",o},this.z=function(){return o.Z()},this.C=function(t,n,r,i,u,f){return e.bezierCurveTo(t,n,r,i,u,f),l=u,s=f,a={x:2*u-r,y:2*f-i},c="C",o},this.c=function(t,e,n,r,i,a){return o.C(t+l,e+s,n+l,r+s,i+l,a+s)},this.S=function(t,e,n,r){var i=u(),a=i.x,l=i.y;return o.C(a,l,t,e,n,r)},this.s=function(t,e,n,r){return o.S(t+l,e+s,n+l,r+s)},this.Q=function(t,n,r,i){return e.quadraticCurveTo(t,n,r,i),l=r,s=i,a={x:2*r-t,y:2*i-n},c="Q",o},this.q=function(t,e,n,r){return o.Q(t+l,e+s,n+l,r+s)},this.T=function(t,e){var n=u(),r=n.x,i=n.y;return o.Q(r,i,t,e)},this.t=function(t,e){return o.T(t+l,e+s)},this.A=function(t,n,r,a,u,f,h){return i(e,l,0,t,n,r,a,u,f),l=f,s=h,c="A",o},this.a=function(t,e,n,r,i,a,c){return o.A(t,e,n,r,i,a+l,c+s)}}},function(t,e){t.exports={d:"M8 24l2.83 2.83L22 15.66V40h4V15.66l11.17 11.17L40 24 24 8 8 24z",width:48,height:48}},function(t,e){t.exports={d:"M40 24l-2.82-2.82L26 32.34V8h-4v24.34L10.84 21.16 8 24l16 16 16-16z",width:48,height:48}},function(t,e){t.exports={d:"M6 34.5V42h7.5l22.13-22.13-7.5-7.5L6 34.5zm35.41-20.41c.78-.78.78-2.05 0-2.83l-4.67-4.67c-.78-.78-2.05-.78-2.83 0l-3.66 3.66 7.5 7.5 3.66-3.66z",width:48,height:48}},function(t,e){t.exports={d:"M38 26H26v12h-4V26H10v-4h12V10h4v12h12v4z",width:48,height:48}},function(t,e){t.exports={d:"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z",width:24,height:24}},function(t,e){t.exports={d:"M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z",width:24,height:24}},function(t,e){t.exports={d:"M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4V6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z",width:24,height:24}},function(t,e,n){var r=n(0).isPromise,o=n(31),i=o.SELECTED_CELL,a=o.SCROLL,l=o.CHANGED_VALUE,s=o.FOCUS_GRID,c=o.BLUR_GRID,u=n(135),f=n(141),d=n(145),g={type:"error",message:void 0},p={error:function(t){return new u(t)},info:function(t){return new d(t)},warning:function(t){return new f(t)}};function y(t){if(!t||r(t))return g;if("string"==typeof t)return{type:"error",message:t,original:t};var e=t.type||"error";return e&&e in p?{type:e.toLowerCase(),message:"".concat(t.message),original:t}:{type:"error",message:"".concat(t),original:t}}function _(t){return!!y(t).message}var m=function(){function t(e,n){h(this,t),this._grid=e,this._messageInstances={},this._bindGridEvent(e,n)}return v(t,[{key:"dispose",value:function(){var t=this._messageInstances;for(var e in t)t[e].dispose();this._messageInstances=null}},{key:"drawCellMessage",value:function(t,e,n,r,o,i){_(t)&&this._getMessageInstanceOfMessage(t).drawCellMessage(t,e,n,r,o,i)}},{key:"_attach",value:function(t,e,n){var r=this._attachInfo,o=this._getMessageInstanceOfMessage(n);r&&r.instance!==o&&r.instance.detachMessageElement(),o.attachMessageElement(t,e,y(n)),this._attachInfo={col:t,row:e,instance:o}}},{key:"_move",value:function(t,e){var n=this._attachInfo;n&&n.col===t&&n.row===e&&n.instance.moveMessageElement(t,e)}},{key:"_detach",value:function(){var t=this._attachInfo;t&&(t.instance.detachMessageElement(),this._attachInfo=null)}},{key:"_bindGridEvent",value:function(t,e){var n=this,r=function(t){var r=e(t.col,t.row);_(r)?n._attach(t.col,t.row,r):n._detach()};t.listen(i,(function(t){t.selected&&(t.before.col===t.col&&t.before.row===t.row||r(t))})),t.listen(a,(function(){var e=t.selection.select;n._move(e.col,e.row)})),t.listen(l,(function(e){var n=t.selection.select;n.col===e.col&&n.row===e.row&&r(e)})),t.listen(s,(function(e){var n=t.selection.select;r(n)})),t.listen(c,(function(t){n._detach()}))}},{key:"_getMessageInstanceOfMessage",value:function(t){var e=this._messageInstances,n=y(t).type;return e[n]||(e[n]=p[n](this._grid))}}]),t}();t.exports=m},function(t,e,n){var r=n(32),o=n(136),i=n(34),l=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"createMessageElementInternal",value:function(){return new o}},{key:"drawCellMessageInternal",value:function(t,e,n,r,o,a){var l=n.bgColor;e.getSelectState().selected&&o.hasFocusGrid()||r.drawBorderWithClip(e,(function(t){i.drawExclamationMarkBox(e,{bgColor:"#ff8a80",color:l},r)}))}}]),e}(r);t.exports=l},function(t,e,n){var r=n(33),o="cheetah-grid__error-message-element",i="".concat(o,"__message"),l=function(t){function e(){var t;return h(this,e),t=a(this,s(e).call(this)),n(139),t._rootElement.classList.add(o),t._messageElement.classList.add(i),t}return c(e,t),e}(r);t.exports=l},function(t,e,n){var r=n(138);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,".cheetah-grid__message-element {\n\tposition: absolute;\n\tmargin-top: -2px;\n\tbox-sizing: border-box;\n\tborder-radius: 0 0 3px 3px;\n\tbackground-color: rgba(250, 250, 250, 0.85);\n\tpadding: 8px 2px;\n\n\tpointer-events: none;\n\tuser-select: none;\n}\n.cheetah-grid__message-element--hidden {\n\tdisplay: none;\n}\n.cheetah-grid__message-element--shown {\n\tdisplay: block;\n}\n.cheetah-grid__message-element__message {\n\tfont-family: Roboto;\n\tfont-size: 12px;\n\tfont-size: .75rem;\n\tmin-height: 1em;\n\tline-height: 1;\n\tdisplay: block;\n\twidth: 100%;\n}\n.cheetah-grid__message-element {\n\tborder-top: solid 1px rgba(0, 0, 0, .87);\n\tcolor: rgba(0, 0, 0, .87);\n}\n",""])},function(t,e,n){var r=n(140);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,".cheetah-grid__error-message-element {\n\tborder-top: solid 1px #FF1744;\n\tcolor: #ff1744;\n}",""])},function(t,e,n){var r=n(32),o=n(142),i=n(34),l=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"createMessageElementInternal",value:function(){return new o}},{key:"drawCellMessageInternal",value:function(t,e,n,r,o,a){var l=n.bgColor;e.getSelectState().selected&&o.hasFocusGrid()||r.drawBorderWithClip(e,(function(t){i.drawExclamationMarkBox(e,{bgColor:"#ff9e80",color:l},r)}))}}]),e}(r);t.exports=l},function(t,e,n){var r=n(33),o="cheetah-grid__warning-message-element",i="".concat(o,"__message"),l=function(t){function e(){var t;return h(this,e),t=a(this,s(e).call(this)),n(143),t._rootElement.classList.add(o),t._messageElement.classList.add(i),t}return c(e,t),e}(r);t.exports=l},function(t,e,n){var r=n(144);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,".cheetah-grid__warning-message-element {\n\tborder-top: solid 1px #dd2c00;\n\tcolor: #dd2c00;\n}",""])},function(t,e,n){var r=n(32),o=n(33),i=n(34),l=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"createMessageElementInternal",value:function(){return new o}},{key:"drawCellMessageInternal",value:function(t,e,n,r,o,a){var l=n.bgColor;e.getSelectState().selected&&o.hasFocusGrid()||r.drawBorderWithClip(e,(function(t){i.drawInfomationMarkBox(e,{bgColor:"#e0e0e0",color:l},r)}))}}]),e}(r);t.exports=l},function(t,e,n){var r=n(31),o=r.SELECTED_CELL,i=r.SCROLL,a=r.CHANGED_VALUE,l=r.MOUSEOVER_CELL,s=r.MOUSEOUT_CELL,c=n(147),u={"overflow-text":function(t){return new c(t)}},f=function(){function t(e){h(this,t),this._grid=e,this._tooltipInstances={},this._bindGridEvent(e)}return v(t,[{key:"dispose",value:function(){var t=this._tooltipInstances;for(var e in t)t[e].dispose();this._tooltipInstances=null}},{key:"_attach",value:function(t,e){var n=this._attachInfo,r=this._getTooltipInstanceInfo(t,e);if(!n||r&&n.instance===r.instance||(n.instance.detachTooltipElement(),this._attachInfo=null),r){var o=r.instance;o.attachTooltipElement(t,e,r.content),this._attachInfo={col:t,row:e,instance:o}}}},{key:"_move",value:function(t,e){var n=this._attachInfo;n&&n.col===t&&n.row===e&&n.instance.moveTooltipElement(t,e)}},{key:"_detach",value:function(){var t=this._attachInfo;t&&(t.instance.detachTooltipElement(),this._attachInfo=null)}},{key:"_isAttachCell",value:function(t,e){var n=this._attachInfo;return!!n&&n.col===t&&n.row===e}},{key:"_bindGridEvent",value:function(t){var e=this;t.listen(l,(function(t){e._attach(t.col,t.row)})),t.listen(s,(function(t){e._detach()})),t.listen(o,(function(t){e._isAttachCell(t.col,t.row)&&e._detach(t)})),t.listen(i,(function(){var t=e._attachInfo;t&&e._move(t.col,t.row)})),t.listen(a,(function(t){e._isAttachCell(t.col,t.row)&&(e._detach(),e._attach(t.col,t.row))}))}},{key:"_getTooltipInstanceInfo",value:function(t,e){var n=this._grid,r=this._tooltipInstances,o=function(t,e,n){var r=t.getCellOverflowText(e,n);return r?{type:"overflow-text",content:r}:null}(n,t,e);if(!o)return null;var i=o.type;return{instance:r[i]||(r[i]=u[i](n)),type:i,content:o.content}}}]),t}();t.exports=f},function(t,e,n){var r=n(148),o=n(149),i=function(t){function e(){return h(this,e),a(this,s(e).apply(this,arguments))}return c(e,t),v(e,[{key:"createTooltipElementInternal",value:function(){return new o}}]),e}(r);t.exports=i},function(t,e,n){var r=function(){function t(e){h(this,t),this._grid=e}return v(t,[{key:"dispose",value:function(){this.detachTooltipElement(),this._tooltipElement&&this._tooltipElement.dispose(),this._tooltipElement=null}},{key:"_getTooltipElement",value:function(){return this._tooltipElement||(this._tooltipElement=this.createTooltipElementInternal())}},{key:"createTooltipElementInternal",value:function(){}},{key:"attachTooltipElement",value:function(t,e,n){this._getTooltipElement().attach(this._grid,t,e,n)}},{key:"moveTooltipElement",value:function(t,e){this._getTooltipElement().move(this._grid,t,e)}},{key:"detachTooltipElement",value:function(){this._getTooltipElement()._detach()}}]),t}();t.exports=r},function(t,e,n){var r=n(3),o=n(10).createElement,i="cheetah-grid__tooltip-element",a="".concat(i,"__content"),l="".concat(i,"--hidden"),s="".concat(i,"--shown"),c=function(){function t(){h(this,t),this._handler=new r;var e=this._rootElement=function(){n(150);var t=o("div",{classList:[i,l]}),e=o("pre",{classList:[a]});return t.appendChild(e),t}();this._messageElement=e.querySelector(".".concat(a))}return v(t,[{key:"dispose",value:function(){this.detach();var t=this._rootElement;t.parentElement&&t.parentElement.removeChild(t),this._handler.dispose(),this._rootElement=null,this._messageElement=null}},{key:"attach",value:function(t,e,n,r){var o=this._rootElement,i=this._messageElement;o.classList.remove(s),o.classList.add(l),this._attachCell(t,e,n)?(o.classList.add(s),o.classList.remove(l),i.textContent=r):this._detach()}},{key:"move",value:function(t,e,n){var r=this._rootElement;this._attachCell(t,e,n)?(r.classList.add(s),r.classList.remove(l)):this._detach()}},{key:"detach",value:function(){this._detach()}},{key:"_detach",value:function(){var t=this._rootElement;t.parentElement&&(t.classList.remove(s),t.classList.add(l))}},{key:"_attachCell",value:function(t,e,n){var r=this._rootElement,o=t.getAttachCellArea(e,n),i=o.element,a=o.rect,l=a.bottom,s=a.left,c=a.width,u=t.frozenRowCount,f=t.frozenColCount;if(n>=u&&u>0){if(l<t.getAttachCellArea(e,u-1).rect.bottom)return!1}else if(l<0)return!1;if(e>=f&&f>0){if(s<t.getAttachCellArea(f-1,n).rect.right)return!1}else if(s<0)return!1;var h=i.offsetHeight,d=i.offsetWidth;return!(h<l||d<s||(r.style.top="".concat(l.toFixed(),"px"),r.style.left="".concat((s+c/2).toFixed(),"px"),r.style["min-width"]="".concat(c.toFixed(),"px"),r.parentElement!==i&&i.appendChild(r),0))}}]),t}();t.exports=c},function(t,e,n){var r=n(151);"string"==typeof r&&(r=[[t.i,r,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};n(7)(r,o),r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(6)(!1)).push([t.i,"@keyframes cheetah-grid__tooltip-element--shown-animation {\n\t0% {\n\t\topacity: 0;\n\t\ttransform: scale(.8) translateX(-60%);\n\t}\n\t100% {\n\t\topacity: 1;\n\t\ttransform: scale(1) translateX(-50%);\n\t}\n}\n\n.cheetah-grid__tooltip-element {\n\tposition: absolute;\n\tbox-sizing: border-box;\n\tborder-radius: 3px;\n\tbackground-color: #232F34;\n\tpadding: 8px;\n\n\tpointer-events: none;\n\tuser-select: none;\n}\n.cheetah-grid__tooltip-element--hidden {\n\topacity: 0;\n\ttransform: translateX(-50%);\n\ttransition: opacity 75ms linear;\n}\n.cheetah-grid__tooltip-element--shown {\n\topacity: 1;\n\ttransform: translateX(-50%);\n\tanimation: cheetah-grid__tooltip-element--shown-animation 150ms ease-out;\n}\n.cheetah-grid__tooltip-element__content {\n\tfont-family: Roboto;\n\tfont-size: 12px;\n\tfont-size: .75rem;\n\tmin-height: 1em;\n\tline-height: 1;\n\twidth: 100%;\n\tdisplay: block;\n\twhite-space: pre-wrap;\n\tmargin: 0;\n\tbox-sizing: border-box;\n}\n.cheetah-grid__tooltip-element {\n\tcolor: #FFFFFF;\n}\n",""])},function(t,e,n){var r=n(57),o=n(65);function i(t,e,n){if(n){var r=t[e];return t[e]=n,r}return t[e]}t.exports={theme:function(t,e){return i(r,t,e)},icon:function(t,e){return i(o,t,e)},icons:function(t){!function(t,e){for(var n in e)t[n]=e[n]}(o,t)}}}])},"object"===("undefined"==typeof exports?"undefined":g(exports))&&"object"===("undefined"==typeof module?"undefined":g(module))?module.exports=u():"function"==typeof define&&define.amd?define([],u):"object"===("undefined"==typeof exports?"undefined":g(exports))?exports.cheetahGrid=u():r.cheetahGrid=u()}).call("undefined"!=typeof global?global:window,"undefined"!=typeof global?global:window)}();
//# sourceMappingURL=cheetahGrid.es5.min.js.map