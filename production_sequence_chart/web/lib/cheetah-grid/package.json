{"name": "cheetah-grid", "version": "0.13.8", "description": "Cheetah Grid is a high performance grid engine that works on canvas", "keywords": ["spreadsheet", "grid", "table", "data", "canvas", "cheetah", "datagrid", "datatable"], "main": "dist/cheetahGrid.es5.js", "unpkg": "dist/cheetahGrid.es5.min.js", "repository": {"type": "git", "url": "https://github.com/future-architect/cheetah-grid.git"}, "author": {"name": "<PERSON><PERSON> ota", "email": "<EMAIL>", "url": "https://www.npmjs.com/~ota-meshi"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/future-architect/cheetah-grid/issues", "email": "<EMAIL>"}, "homepage": "https://future-architect.github.io/cheetah-grid/", "files": ["src/js/", "dist/", "tools/src/", "webpack-loader/", "index.d.ts"], "scripts": {"test": "karma start --no-auto-watch --single-run --browsers Chrome_travis_ci", "lint": "npm run eslint", "test:once": "karma start --min  --no-auto-watch --single-run", "test:watch": "karma start --min", "test:ff": "karma start --browsers Firefox", "test:ie": "karma start --min --browsers IE_no_addons", "test:chrome": "karma start --browsers Chrome", "build": "npm run webpack", "watch": "cross-env NO_UPDATE_NOTIFIER=1 webpack --watch", "webpack": "cross-env NO_UPDATE_NOTIFIER=1 webpack", "eslint": "eslint .", "eslint:fix": "eslint . --fix", "test:font-svg-to-icons-js-loader": "node webpack/test/font-svg-to-icons-js-loader/test.js", "test:svg-to-icon-js-loader": "node webpack/test/svg-to-icon-js-loader/test.js", "test:loader": "npm run test:font-svg-to-icons-js-loader && npm run test:svg-to-icon-js-loader", "build:ci": "npm run build"}, "directories": {"src": "src/js", "test": "src/test/specs"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "babel-loader": "^8.0.2", "cheetah-grid-icon-svg-loader": "^0.13.0", "cross-env": "^5.2.0", "css-loader": "^1.0.0", "eslint": "^5.16.0", "eslint-plugin-html": "^4.0.5", "image-matcher": "^0.1.4", "jasmine": "^2.99.0", "karma": "^3.0.0", "karma-babel-preprocessor": "^8.0.0-beta.0", "karma-chrome-launcher": "^2.2.0", "karma-cli": "^1.0.1", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.1.0", "karma-htmlfile-reporter": "^0.3.6", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^1.1.2", "karma-junit-reporter": "^1.2.0", "karma-mocha-reporter": "^2.2.5", "karma-remap-istanbul": "^0.6.0", "material-design-icons": "^3.0.1", "promise-polyfills": "^7.0.4", "raw-loader": "^0.5.1", "style-loader": "^0.23.0", "webpack": "^4.19.1", "webpack-cli": "^3.1.0", "webpack-livereload-plugin": "^2.1.1", "webpack-sources": "^1.3.0", "wrapper-webpack-plugin": "^2.0.0"}}