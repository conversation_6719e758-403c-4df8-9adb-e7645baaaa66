'use strict';

const {
	event: {
		cancel: cancelEvent,
	}
} = require('../../internal/utils');
const {isDisabledRecord, isReadOnlyRecord} = require('./action-utils');
const Editor = require('./Editor');
const {EVENT_TYPE: {
	INPUT_CELL,
	PASTE_CELL,
	EDITABLEINPUT_CELL,
	SELECTED_CELL,
	DBLCLICK_CELL,
	DBLTAP_CELL,
	KEYDOWN,
	MODIFY_STATUS_EDITABLEINPUT_CELL,
	SCROLL
}} = require('../../core/DrawGrid');
const KEY_ENTER = 13;
const KEY_F2 = 113;

class BaseInputEditor extends Editor {
	constructor(option = {}) {
		super(option);
	}
	clone() {
		return new BaseInputEditor(this);
	}
	onInputCellInternal(grid, cell, inputValue) {
		throw new Error();
	}
	onOpenCellInternal(grid, cell) {
		throw new Error();
	}
	onChangeSelectCellInternal(grid, cell, selected) {
		throw new Error();
	}
	onSetInputAttrsInternal(grid, cell, input) {
		throw new Error();
	}
	onGridScrollInternal(grid) {
		throw new Error();
	}
	bindGridEvent(grid, col, util) {
		const open = (cell) => {
			if (
				isReadOnlyRecord(this.readOnly, grid, cell.row) ||
				isDisabledRecord(this.disabled, grid, cell.row)
			) {
				return;
			}
			this.onOpenCellInternal(grid, cell);
		};

		const input = (cell, value) => {
			if (
				isReadOnlyRecord(this.readOnly, grid, cell.row) ||
				isDisabledRecord(this.disabled, grid, cell.row)
			) {
				return;
			}
			this.onInputCellInternal(grid, cell, value);
		};
		return [
			grid.listen(INPUT_CELL, (e) => {
				if (!util.isTarget(e.col, e.row)) {
					return;
				}
				input({
					col: e.col,
					row: e.row
				}, e.value);
			}),
			grid.listen(PASTE_CELL, (e) => {
				if (e.multi) {
					// ignore multi cell values
					return;
				}
				if (!util.isTarget(e.col, e.row)) {
					return;
				}
				cancelEvent(e.event);
				input({
					col: e.col,
					row: e.row
				}, e.normalizeValue);
			}),
			grid.listen(DBLCLICK_CELL, (cell) => {
				if (!util.isTarget(cell.col, cell.row)) {
					return;
				}
				open({
					col: cell.col,
					row: cell.row
				});
			}),
			grid.listen(DBLTAP_CELL, (e) => {
				if (!util.isTarget(e.col, e.row)) {
					return;
				}
				open({
					col: e.col,
					row: e.row
				});

				cancelEvent(e.event);
			}),
			grid.listen(KEYDOWN, (keyCode, e) => {
				if (keyCode !== KEY_F2 && keyCode !== KEY_ENTER) {
					return;
				}
				const sel = grid.selection.select;
				if (!util.isTarget(sel.col, sel.row)) {
					return;
				}
				open({
					col: sel.col,
					row: sel.row
				});
			}),
			grid.listen(SELECTED_CELL, (e) => {
				this.onChangeSelectCellInternal(grid, {col: e.col, row: e.row}, e.selected);
			}),
			grid.listen(SCROLL, () => {
				this.onGridScrollInternal(grid);
			}),
			grid.listen(EDITABLEINPUT_CELL, (cell) => {
				if (!util.isTarget(cell.col, cell.row)) {
					return false;
				}
				if (
					isReadOnlyRecord(this.readOnly, grid, cell.row) ||
					isDisabledRecord(this.disabled, grid, cell.row)
				) {
					return false;
				}
				return true;
			}),
			grid.listen(MODIFY_STATUS_EDITABLEINPUT_CELL, (cell) => {
				if (!util.isTarget(cell.col, cell.row)) {
					return;
				}
				if (
					isReadOnlyRecord(this.readOnly, grid, cell.row) ||
					isDisabledRecord(this.disabled, grid, cell.row)
				) {
					return;
				}
				this.onSetInputAttrsInternal(grid, {
					col: cell.col,
					row: cell.row
				}, cell.input);
			}),

		];
	}
}

module.exports = BaseInputEditor;
