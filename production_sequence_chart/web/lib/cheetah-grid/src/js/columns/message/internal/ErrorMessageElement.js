'use strict';
const MessageElement = require('./MessageElement');

const CLASSNAME = 'cheetah-grid__error-message-element';
const MESSAGE_CLASSNAME = `${CLASSNAME}__message`;


class ErrorMessageElement extends MessageElement {
	constructor() {
		super();
		require('./ErrorMessageElement.css');
		this._rootElement.classList.add(CLASSNAME);
		this._messageElement.classList.add(MESSAGE_CLASSNAME);
	}
}

module.exports = ErrorMessageElement;