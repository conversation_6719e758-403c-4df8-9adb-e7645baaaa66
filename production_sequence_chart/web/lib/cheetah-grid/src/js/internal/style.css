/**
 * core styles 
 */
.cheetah-grid .grid-scrollable {
	height: 100%;
	width: 100%;
	position: absolute;
	overflow: scroll;
}
.cheetah-grid .grid-scroll-end-point {
	opacity: 0;
	position: relative;
}
.cheetah-grid {
	position: relative;
	width: 100%;
	height: 100%;
}
.cheetah-grid > canvas {
	position: absolute;
	width: 0;
	height: 0;
}
.cheetah-grid .grid-focus-control {
	position: relative !important;
	width: 1px;
	height: 1px;
	opacity: 0;
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	pointer-events: none;
	max-width: 0;
	max-height: 0;
	float: none !important;
}
.cheetah-grid input.grid-focus-control::-ms-clear {
	visibility:hidden;
}
.cheetah-grid input.grid-focus-control.composition {
	opacity: 1;
	max-width: none;
	max-height: none;
}