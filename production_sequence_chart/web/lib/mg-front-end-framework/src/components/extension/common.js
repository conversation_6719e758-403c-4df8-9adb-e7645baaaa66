// 设置datazoom
export const setDataZoom = function (option) {
  if (option) {
    option['dataZoom'] = [
      {
        type: 'slider',
        filterMode: 'filter',
        showDataShadow: false,
        bottom: 'auto',
        height: 10,
        borderColor: 'transparent',
        backgroundColor: '#e2e2e2',
        handleIcon: 'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z', // jshint ignore:line
        handleSize: 20,
        handleStyle: {
          shadowBlur: 6,
          shadowOffsetX: 1,
          shadowOffsetY: 2,
          shadowColor: '#aaa'
        },
        labelFormatter: ''
      }
    ]
  }
}

// 给echarts实例添加事件
export const initEvent = function (eventObj, chartInstance) {
  if (!chartInstance || !eventObj) {
    return
  }
  const eventNames = Object.keys(eventObj)
  eventNames.length && eventNames.forEach(function (name) {
    // 防止echarts实例重复绑定事件
    chartInstance._$handlers[name] && (chartInstance._$handlers[name].length = 0)
    chartInstance.on(name, point => eventObj[name](point))
  })
}

// 比较2个对象是否相同（至多比较第一层属性）
export const isSameObj = function (obj1, obj2) {
  if (obj1 === obj2) {
    return true
  }

  if (typeof obj1 !== typeof obj2) {
    return false
  }

  if (Array.isArray(obj1) && Array.isArray(obj2)) {
    if (obj1.length !== obj2.length) {
      return false
    }
    for (let i = 0; i < obj1.length; i++) {
      if (obj1[i] !== obj2[i]) {
        return false
      }
    }
  }

  if (typeof obj1 === 'object' && typeof obj2 === 'object') {
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)
    if (keys1.length !== keys2.length) {
      return false
    }

    for (let i = 0; i < keys1.length; i++) {
      if (obj1[keys1[i]] !== obj2[keys2[i]]) {
        return false
      }
    }
  }

  return true
}
