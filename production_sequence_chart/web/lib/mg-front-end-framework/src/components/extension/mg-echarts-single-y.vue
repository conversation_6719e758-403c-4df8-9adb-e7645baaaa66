<template>
  <div class="relative-position" :style="{width: width, height: height}">
    <div :ref="refName" class="full-width full-height absolute"></div>
    <div class="absolute" style="right: 0;width: 100px">
        <!--<q-select :value="type"
                  @input="typeChange"
                  float-label="图表类型"
                  radio
                  :options="chartTypes" />-->

        <div class="q-mt-md q-ml-md text-right" v-if="showMarkpointLegend">
          <template v-for="point in pointType">
            <div class="row">
              <div>
                <span class="legend-icon" :style="{'background-color': point.color}"></span>
              </div>
              <div class="q-ml-sm">
                <span class="legend-content">{{ point.label }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { uid } from 'quasar'
import { setDataZoom } from './common'
import { maxTypeChangeSize, colorSeries } from './chart-constant.json'
export default {
  props: {
    width: { // 图表宽度
      type: String,
      default: '100%'
    },
    height: { // 图表高度
      type: String,
      default: '100%'
    },
    type: {
      type: String,
      default: 'line'
    },
    title: {
      type: String,
      default: ''
    },
    xName: { // x axis标题
      type: String,
      default: ''
    },
    yName: { // y axis标题
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    },
    click: { // 响应图表点击事件 click(point, limits)
      type: Function,
      default: null
    },
    alertMethod: {
      type: Function,
      default: null
    }
  },
  data () {
    return {
      showMarkpointLegend: false, // 是否显示markpoint图例
      chart: null,
      refName: uid(),
      decimal: 4,
      chartTypes: [
        {
          label: '折线图',
          value: 'line'
        },
        {
          label: '柱形图',
          value: 'bar'
        }
      ],
      pointType: {
        error: {
          label: '异常',
          color: '#b71c1c'
        },
        warning: {
          label: '警告',
          color: '#f2c037'
        },
        normal: {
          label: '正常',
          color: '#21ba45'
        },
        good: {
          label: '良好',
          color: '#f48fb1'
        },
        invalid: {
          label: '未知',
          color: '#91989F'
        }
      },
      limitMap: {
        min: '下限',
        max: '上限',
        optimum: '最优'
      }
    }
  },
  computed: {
    isChart () {
      return this.data && this.data.data && this.data.data.length
    }
  },
  mounted () {
    this.initChart()
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy () {
    this.clearChart()
  },
  destroyed () {
    window.removeEventListener('resize', this.resizeChart)
  },
  watch: {
    data: {
      immediate: true,
      handler (value) {
        if (!value || !value.data) {
          return
        }
        const list = value.data
        list.forEach(item => {
          if (item.value && String(item.value).indexOf('.') > -1) {
            const floatNum = String(item.value).split('.')[1].length
            floatNum > this.decimal && (item.value = Number(item.value).toFixed(this.decimal))
          }
        })
        this.initChart()
      }
    },
    type () {
      this.initChart()
    },
    title (value) {
      this.reRenderChart({
        title: {
          text: value,
          left: 'center'
        }
      })
    },
    yName (value) {
      if (!this.data) {
        return
      }
      this.reRenderChart({
        yAxis: {
          name: this.getAxisName(value, this.data.yUnit)
        }
      })
    },
    width () {
      this.resizeChart()
    },
    height () {
      this.resizeChart()
    }
  },
  methods: {
    typeChange (value) {
      this.$emit('type-change', value)
    },
    reRenderChart (option) {
      if (!this.chart) {
        return
      }
      this.chart.setOption(option)
    },
    resizeChart () {
      this.$nextTick(function () {
        this.chart && this.chart.resize()
      })
    },
    getAxisName (name, unit) {
      return name + (unit ? ': ' + unit : '')
    },
    setOption () {
      if (!this.data) {
        return
      }

      const xAxisName = this.getAxisName(this.xName, this.data.xUnit)
      const yAxisName = this.getAxisName(this.yName, this.data.yUnit)
      const renderData = this.data.data
      const limit = this.data.limit
      let series = [{
        data: renderData.map(item => [item.time, item.value]),
        name: this.yName,
        type: this.type
      }]
      this.showMarkpointLegend = typeof this.alertMethod === 'function' && this.alertMethod()
      if (this.showMarkpointLegend) {
        let points = []
        renderData.forEach(function (item, index) {
          if (item.type) {
            points.push({
              coord: [index, item.value],
              value: item.value,
              itemStyle: {
                color: this.pointType[item.type]['color']
              }
            })
          }
        }.bind(this))
        points.length && (series[0]['markPoint'] = {
          data: points
        })
      }

      this.setLimits(series, renderData, limit)

      const extralOpt = {
        tooltip: {
          trigger: 'axis'
        },
        color: colorSeries,
        title: {
          text: this.title,
          left: 'center'
        },
        legend: {
          show: true,
          top: 'bottom'
        },
        xAxis: {
          type: 'category',
          name: xAxisName
        },
        yAxis: {
          type: 'value',
          name: yAxisName
        },
        series: series
      }

      if (renderData.length > maxTypeChangeSize) {
        setDataZoom(extralOpt)
      }

      if (this.type === 'line' && limit) {
        extralOpt['yAxis']['max'] = function (value) {
          return this.getLimitValue(value, limit, 'max', 1)
        }.bind(this)
        extralOpt['yAxis']['min'] = function (value) {
          return this.getLimitValue(value, limit, 'min', -1)
        }.bind(this)
      }

      this.chart.hideLoading()
      if (!this.chart.getOption()) {
        this.chart.setOption(extralOpt)
      } else {
        this.chart.setOption(extralOpt, true)
      }
    },
    getLimitValue (target, limit, fieldName, prefix) { // prefix: 1 max; -1 min
      if (limit) {
        const limitMax = Number(limit.max) ? limit.max : -Infinity
        const limitMin = Number(limit.min) ? limit.min : Infinity
        const gap = Math.max(target.max, limitMax) - Math.min(target.min, limitMin)
        const result = Number(target[fieldName]) + prefix * gap * 0.05 + prefix * 0.005
        return result.toFixed(2)
      }
    },
    setLimits (series, renderData, limits) {
      if (!limits) {
        return
      }

      const lineKeys = Object.keys(limits)
      if (!lineKeys.length) {
        return
      }

      lineKeys.forEach(function (key) {
        let list = renderData.map(point => [point.time, point[key] || limits[key]])
        series.push({
          visualMap: false,
          name: this.limitMap[key] || key,
          data: list,
          type: 'line',
          lineStyle: {
            type: 'dashed'
          }
        })
      }.bind(this))
    },
    initChart () {
      if (this.isChart) {
        if (!this.chart && this.$refs[this.refName]) {
          this.chart = echarts.init(this.$refs[this.refName])
          this.clickEvent()
        }
        if (this.chart) {
          this.chart.showLoading()
          this.setOption()
        }
      } else {
        this.clearChart()
      }
    },
    clickEvent () {
      if (typeof this.click === 'function') {
        this.chart.on('click', function (point) {
          this.click(point)
        }.bind(this))
      }
    },
    clearChart () {
      if (!this.chart) {
        return
      }

      this.chart.clear()
      this.chart.dispose()
      this.chart = null
    }
  }
}
</script>

<style lang="stylus" scoped>
  .legend-icon
    display inline-block
    width 20px
    height 10px
    border-radius 2px
  .legend-content
    font-size 14px
</style>
