const NAME_CACHE_KEY = 'user_cache_list'
import secret from './secret'
export default {
  props: {
    cachePrefix: String, // 缓存前缀：用于区分不同功能的缓存，也可区分不同组织下的缓存
    cacheName: { // 是否缓存已选的用户名
      type: Boolean,
      default: false
    },
    cachedNameNum: { // 缓存的用户名数量
      type: Number,
      default: 10
    }
  },
  computed: {
    cacheKey () {
      return (this.cachePrefix ? (this.cachePrefix + '_') : '') + NAME_CACHE_KEY
    }
  },
  data () {
    return {
      cachedList: []
    }
  },
  created () {
    if (!this.cacheName) return

    // 获取用户名缓存，如果没有，则设置缓存
    const str = localStorage.getItem(this.cacheKey)
    if (str) {
      this.cachedList = secret.Decrypt(str)
    } else {
      this.cache2Storage()
    }
  },
  methods: {
    // 缓存用户名：当缓存数量大于既定数量，删除最先缓存的用户名
    addToCache (user) { // user = { user_name, user_password }
      if (!this.cacheName) return

      const sameUser = this.cachedList.find(_ => _.user_name === user.user_name)
      if (sameUser) {
        // 已缓存的用户名，更新密码
        user.user_password && (sameUser.user_password = user.user_password)
      } else {
        this.cachedList.unshift(user)
      }
      // 最多缓存10个用户名
      if (this.cachedList.length >= this.cachedNameNum) {
        this.cachedList.pop()
      }

      this.cache2Storage()
    },
    clearCache () { // 删除用户名缓存
      if (!this.cacheName) return

      this.cachedList = []
      this.cache2Storage()
    },
    clearCacheByName (name) { // 从缓存中删除指定name的项
      if (!this.cacheName) return

      const index = this.cachedList.findIndex(item => item.user_name === name)
      if (index !== -1) {
        this.cachedList.splice(index, 1)
        this.cache2Storage()
      }
    },
    cache2Storage () { // 缓存到localstorage
      localStorage.setItem(this.cacheKey, secret.Encrypt(this.cachedList))
    }
  }
}