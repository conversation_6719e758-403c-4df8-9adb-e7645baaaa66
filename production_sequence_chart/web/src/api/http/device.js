import { _get, _post } from './httpWrap'
// import template from 'assets/data.json'

export default {
    getDeviceSummary (param, succ, fail) { // 查询车间产线
        _get(`api/production_sequence_chart/production_sequence_summary/${param}`, succ, fail)
    },
    getDeviceBeats (param, succ, fail) { // 查询节拍时序图数据
        _post('api/production_sequence_chart/production_sequence_data', param, succ, fail)
    },
    commitRatedBeat (param, succ, fail) { // 提交额定节拍
        _post('api/production_sequence_chart/set_manual_jp', param, succ, fail)
    },
    getDeviceData (param, succ, fail) { // 查询不良品时序图数据
        _post('api/production_sequence_chart/production_sequence_ng', param, succ, fail)
    },
    getProductCapacity (param, succ, fail) { // 获取总产量
        _post('api/production_sequence_chart/production_capacity', param, succ, fail)
    },
    getDeviceException (param, succ) { // 异常信息
        _post('api/production_index_analysis/v2/aggregation/index_exception_detail', param, succ)
    },
    modifyDataByAdmin (param, succ) { // 修改时序图数据
        _post('api/production_sequence_chart/modify_data', param, succ, null)
    },
    modifyDataByRoot (param, succ) { // 修改时序图数据
        _post('api/production_sequence_chart/modify_chart_data', param, succ, null)
    }
}
