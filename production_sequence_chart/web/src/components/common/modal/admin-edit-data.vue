<template>
    <mg-modal title="修改数据请求" :open.sync="show" no-backdrop-dismiss>
        <div slot="body" class="q-pa-md">
            <el-form :model="formData" ref="rule-form" :rules="rules" size="small" label-position="left">
                <el-form-item label="日期：" label-width="45px" class="inline-block vertical-middle q-mr-md">
                    <span>{{staticData.date}}</span>
                </el-form-item>
                <el-form-item label="产线：" label-width="45px" class="inline-block vertical-middle q-mr-md">
                    <span>{{staticData.pipeline}}</span>
                </el-form-item>
                <el-form-item label="设备名：" label-width="60px" class="inline-block vertical-middle q-mr-md">
                    <span>{{staticData.deviceName}}</span>
                </el-form-item>
                <el-form-item label="请选择修改时间：" prop="timestamp" label-width="130px">
                    <el-time-picker v-model="formData.timestamp" value-format="yyyy-MM-dd HH:mm:ss" :disabled="timestampDisable" size="small" placeholder="请选择修改时间"></el-time-picker>
                </el-form-item>
                <el-form-item label="修改对象：" label-width="70px" class="inline-block vertical-middle q-mr-md">
                    <span>{{staticData.indexName}}</span>
                </el-form-item>
                <el-form-item label="现有数量：" label-width="70px" class="inline-block vertical-middle">
                    <span>{{staticData.oldValue}}个</span>
                </el-form-item>
                <el-form-item label="请输入修改后的数量：" prop="value" label-width="160px">
                    <el-input-number v-model="formData.value"></el-input-number>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="text-center">
            <el-button size="small" class="q-mr-md" @click="cancel">取消</el-button>
            <el-button type="primary" size="small" @click="confirm">确定</el-button>
      </div>
    </mg-modal>
</template>

<script>
import { date } from 'quasar'
export default {
    props: {
        data: {
            type: Object
        },
        ok: Function
    },
    data () {
        return {
            show: false,
            operate: 'add', // 'edit'
            staticData: {
                date: '',
                pipeline: '',
                device: '',
                deviceName: '',
                index: '',
                indexName: '',
                oldValue: 0
            },
            formData: {
                timestamp: null,
                value: 0
            },
            rules: {
                timestamp: [
                    { required: true, message: '必填', trigger: ['blur', 'change'] }
                ],
                value: [
                    { required: true, message: '必填', trigger: ['blur', 'change'] },
                    { type: 'number', min: 0, message: '数量需为非0整数', trigger: ['blur', 'change'] }
                ]
            }
        }
    },
    computed: {
        timestampDisable () {
            // return this.operate === 'edit'
            return true
        }
    },
    methods: {
        confirm () {
            this.$refs['rule-form'].validate(valid => {
                if (valid && (typeof this.ok === 'function')) {
                    this.ok(this.staticData, this.formData, (res) => {
                        this.showNotify({ message: res.info, color: 'positive' })
                        this.show = false
                    })
                }
            })
        },
        cancel () {
            this.show = false
            this.staticData = {
                date: '',
                pipeline: '',
                device: '',
                deviceName: '',
                index: '',
                indexName: '',
                oldValue: 0
            }
            this.formData = {
                timestamp: date.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                value: 0
            }
        },
        triggerShow (staticParam, formParam, operate) { // operate: 'edit'|'add'
            Object.assign(this.staticData, staticParam)
            Object.assign(this.formData, formParam)
            this.operate = operate
            this.show = true
        }
    }
}
</script>

<style lang="stylus">
.el-form-item__label
    padding 0 !important
.el-time-panel
    z-index 9999 !important
</style>
