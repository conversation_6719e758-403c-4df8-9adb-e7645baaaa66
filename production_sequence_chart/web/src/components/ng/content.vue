<template>
    <div class="full-width full-height">
        <div class="q-px-sm q-py-sm full-width fixed-bar">
            <div class="row items-center justify-between">
                <div class="row">
                    <el-select v-model="workshopCode" size="mini" class="q-mr-sm form-comp" placeholder="车间">
                        <el-option v-for="item in workshopList" :key="item.value" v-bind="item"></el-option>
                    </el-select>
                    <el-select v-model="pipelineCode" size="mini" class="q-mr-sm form-comp" placeholder="产线">
                        <el-option v-for="item in pipelineList" :key="item.value" v-bind="item"></el-option>
                    </el-select>
                    <el-date-picker v-model="datetime"
                                    value-format="yyyy-MM-dd"
                                    size="mini" type="date"
                                    class="form-comp q-mr-sm"
                                    :picker-options="pickerOptions" />
                    <div class="q-mr-sm">
                        <el-button type="primary" size="mini" icon="el-icon-plus">设备对比</el-button>
                        <q-popover anchor="bottom right" touch-position>
                            <div class="q-px-sm q-mt-sm column">
                                <div v-show="!deviceList.length" class="text-grey">无可选设备</div>
                                <template v-show="deviceList.length">
                                    <q-checkbox v-show="deviceList.length"
                                                color="secondary"
                                                class="q-ma-md"
                                                :value="all"
                                                @input="allCheck"
                                                label="全选" />
                                    <q-checkbox v-for="device in deviceList"
                                                :key="device.value"
                                                color="secondary"
                                                class="q-ma-md"
                                                v-model="checkedCodes"
                                                :val="device.value" :label="device.label" />
                                </template>
                            </div>
                        </q-popover>
                    </div>
                    <el-button type="primary" size="mini" @click.stop="debounce(deviceCodes, true)">刷新</el-button>
                </div>
                <!-- 图例 -->
                <div class="row justify-end">
                    <div class="non-selectable row justify-center legend-container">
                        <div v-for="item in typeList"
                             :key="item.value"
                             class="cursor-pointer bar-legend"
                             :class="{
                                'legend-selected': types.includes(item.value),
                                'status-7': item.value === 7
                             }"
                             :style="{ 'background-color': item.color }"
                             @click="selectStatus(item)">
                            {{ item.name }}
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <mg-echarts type="zoom-time"
                            :events="events"
                            :data="datazoomItem"
                            :left="100"
                            width="100%" height="40px"></mg-echarts>
            </div>
        </div>
        <device-list :data="chartData"
                     :data-zoom="datazoomObj"
                     :time-range="timeRange"
                     :pipeline-name="pipelineName"
                     :datetime="datetime"
                     class="chart-body q-px-sm"
                     @refresh="debounce(deviceCodes, true)"></device-list>
    </div>
</template>

<script>
import { mapMutations } from 'vuex'
import DeviceList from './device-list'
import { date, Loading } from 'quasar'
import http from 'http/device'
import { deviceType2color, deviceType2name } from 'assets/constant'
const ItemReasons = [
    { value: '-1', label: '混合中断', count: 1 },
    { value: '1', label: '运行时间', count: 1 },
    { value: '2', label: '待机', count: 1 },
    { value: '3', label: '故障', count: 1 },
    { value: '4', label: '断流', count: 1 },
    { value: '5', label: '缺料', count: 1 },
    { value: '6', label: '满料', count: 1 }
]
export default {
    components: {
        DeviceList
    },
    data () {
        const typeList = deviceType2name.map((_, i) => ({ value: i, name: _, color: deviceType2color[i] }))
        return {
            types: [],
            typeList,

            pickerOptions: {
                disabledDate (time) {
                    return time.getTime() > Date.now()
                }
            },
            workshopCode: this.$route.query.workShopCode || localStorage.getItem('workshopCode') || '', // 当前车间code
            workshopList: [], // 车间列表
            pipelineCode: this.$route.query.pipelineCode || localStorage.getItem('pipelineCode') || '', // 当前产线code
            datetime: this.$route.query.date || date.formatDate(Date.now(), 'YYYY-MM-DD'),
            pipelineMap: {}, // { workshopCode: pipelineList }
            deviceMap: {}, // { workshopCode_pipelineCode: deviceList }
            deviceCodes: this.$route.query.checkedCode ? [this.$route.query.checkedCode] : [], // 当前显示的设备code, 用于与checkedCodes对比
            checkedCodes: this.$route.query.checkedCode ? [this.$route.query.checkedCode] : [], // checkbox 选中的code
            all: false, // 设备是否全选

            chartData: [], // 时序图数据
            timeRange: [],

            datazoomItem: {}, // 主要记录时间轴的开始时间和结束时间
            datazoomObj: {}, // 时间拖拽参数
            events: { // 时间轴事件
                datazoom: this.setZoom
            },

            timer: null,
            timeout: 200
        }
    },
    computed: {
        pipelineList () { // 产线列表
            return this.pipelineMap[this.workshopCode] || []
        },
        deviceList () { // 设备列表
            return this.deviceMap[this.workshopCode + '_' + this.pipelineCode] || []
        },
        pipelineName () { // 产线名称
            const ppl = this.pipelineList.find(_ => _['value'] === this.pipelineCode)
            return ppl ? ppl.label : ''
        }
    },
    watch: {
        workshopCode (code) { // 切换车间时
            this.all = false // 车间切换时,取消全选
            this.setStorage('workshopCode', code)
            if (code) {
                const pipelineList = this.pipelineMap[code] || []
                // this.pipelineCode值为空或不在this.pipelineList中, 则需重新设置this.pipelineCode
                const pipeline = this.pipelineCode
                if (!pipeline || pipelineList.findIndex(_ => _['value'] === pipeline) === -1) {
                    this.pipelineCode = pipelineList[0] ? pipelineList[0]['value'] : ''
                }
            }
        },
        pipelineCode (newCode) { // 切换产线
            this.all = false // 产线切换时,取消全选
            this.setStorage('pipelineCode', newCode)
            if (newCode) { // 清除上次选中设备
                const deviceList = this.deviceMap[this.workshopCode + '_' + this.pipelineCode] || []
                if (!deviceList.length) {
                    this.checkedCodes = []
                    return
                }
                let reset = !this.checkedCodes.length
                let checkedList = [] // 存储产线切换时要选中的设备
                if (!reset) {
                    checkedList = this.checkedCodes.filter(_ => deviceList.some(d => d['value'] === _))
                    reset = checkedList.length
                }
                if (!checkedList.length) {
                    checkedList.push(deviceList[0]['value'])
                }
                this.checkedCodes = checkedList
            }
        },
        datetime () {
            this.debounce(this.deviceCodes, true)
        },
        checkedCodes (newList) {
            if (!newList.length) { // 无设备可选
                this.chartData = []
                this.deviceCodes = []
                this.timer && clearTimeout(this.timer)
                this.all = false
                return
            }
            // 取消选中的项，从chartData中删除
            const uncheckedCodes = this.deviceCodes.filter(item => !newList.includes(item))
            if (uncheckedCodes && uncheckedCodes.length) {
                this.chartData = this.chartData.filter(_ => !uncheckedCodes.includes(_.code))
                // 删除手动加上的数据
                this.chartData.forEach(_ => {
                    if (_.status.length) {
                        _.status[0].mark && _.status.shift()
                        _.status[_.status.length - 1].mark && _.status.pop()
                    }
                })
                this.setTimeRange(this.chartData)
            }
            // 选中的项，去查询设备信息
            const checkedCodes = newList.filter(code => !this.deviceCodes.includes(code))
            this.deviceCodes = JSON.parse(JSON.stringify(newList)) // 更新当前选中的项
            if (checkedCodes.length) {
                this.debounce(checkedCodes)
            } else { // 及时清除不必要的查询
                this.timer && clearTimeout(this.timer)
            }
            // 点击勾选了全部设备时，全选项也要勾选
            this.all = this.deviceCodes.length === this.deviceList.length
        }
    },
    created () {
        this.setPageTitle('产量计数时序图')
        // 若有值, 则保存到localStorage
        if (this.workshopCode && this.workshopCode !== localStorage.getItem('workshopCode')) {
            localStorage.setItem('workshopCode', this.workshopCode)
        }
        if (this.pipelineCode && this.pipelineCode !== localStorage.getItem('workshopCode')) {
            localStorage.setItem('pipelineCode', this.pipelineCode)
        }
        this.getSummaryInfo()
        if(this.pipelineCode && this.workshopCode && this.checkedCodes.length && this.datetime) {
            this.debounce(this.deviceCodes, true)
        }
    },
    methods: {
        ...mapMutations(process.env.APP_SCOPE_NAME, ['setPageTitle']),
        selectStatus ({ value }) { // 选中图例时触发条状图渲染（mg-echarts-switch中监听types变化）
            if (!this.types.includes(value)) {
                this.types.push(value)
            } else {
                this.types.splice(this.types.indexOf(value), 1)
            }
            this.chartData.forEach(_ => (_.types = this.types))
        },
        allCheck () {
            this.all = !this.all
            this.checkedCodes = this.all ? this.deviceList.map(_ => _['value']) : []
        },
        setStorage (key, value) {
            const oldValue = localStorage.getItem(key)
            if (oldValue !== value) {
                localStorage.setItem(key, value)
            }
        },
        getSummaryInfo () { // 获取车间、产线信息
            // 查询车间产线，并在回调中查询选中车间产线对应的设备信息
            http.getDeviceSummary('all', res => {
                if (!this.responseValidate(res)) {
                    return
                }
                const data = res.data
                const workshops = res.data.workshop_list || []
                // 车间 产线 设备 映射关系解析f
                this.setPipelineAndDevice(workshops)
                if (workshops.length) {
                    const code = this.pipelineCode
                    let index = 0 // 默认选中第一个车间
                    if (code) {
                        index = workshops.findIndex(_ => {
                            const list = _['pipeline_list'] || []
                            return list.some(p => p['value'] === code)
                        })
                        index === -1 && (index = 0)
                    }
                    !this.workshopCode && (this.workshopCode = workshops[index]['value'])
                    !this.pipelineCode && (this.pipelineCode = this.pipelineMap[this.workshopCode][0]['value'])
                    // 默认显示第一个设备
                    if (!this.deviceCodes.length) {
                        const firstDeviceList = this.deviceMap[this.workshopCode + '_' + this.pipelineCode] || []
                        const firstCode = firstDeviceList[0] ? firstDeviceList[0]['value'] : ''
                        this.checkedCodes.push(firstCode)
                    }
                }
            })
        },
        debounce () { // 节流
            const args = arguments
            this.timer && clearTimeout(this.timer)
            this.timer = setTimeout(function () {
                this.getDeviceData(...args)
            }.bind(this), this.timeout)
        },
        // 获取数据 overwrite: true, 覆盖chartData数据; false, 添加一条新数据到chartData
        getDeviceData (deviceCode = [], overwrite = false) {
            Loading.show()
            const param = {
                // workshop: this.workshopCode,
                pipeline_code: this.pipelineCode,
                timestamp: [this.datetime],
                device_code_list: deviceCode.length ? deviceCode : this.deviceCodes
            }
            http.getDeviceData(param, res => {
                Loading.hide()
                if (!this.responseValidate(res)) {
                    this.chartData = []
                    return
                }
                function dataFilter (data) {
                    let newData = data.map((item1) => {
                        let itemData = item1.data.map((item1Data) => {
                            return {
                                time: item1Data.time,
                                count: item1Data.value > 0 ? 1 : 0,
                                //value决定柱状图颜色，不存在status_code时默认value为1，1对应的颜色在`assets/constant`的paColors中
                                value: item1Data.status_code ? item1Data.status_code : 1,
                                showValue: item1Data.value
                            }
                        })
                        return {
                            code: item1.code,
                            name: item1.name,
                            seq: item1.seq,
                            total: 0,
                            data: itemData,
                            reasons: ItemReasons,
                            status: item1.status
                        }
                    })
                    return newData
                }
                let responseData = res.data || []
                responseData = dataFilter(responseData)
                // 初始化types(用于记录已勾选的状态图例选项)
                responseData.length && responseData.forEach(item => (item.types = this.types))
                if (overwrite) {
                    this.chartData = responseData
                } else {
                    // checkbox触发的查询，先验证接口数据是否仍是选中状态（补充节流之外的异常情况）
                    const deviceCodes = this.deviceCodes
                    const list = responseData.filter(item => deviceCodes.includes(item.code))
                    if (list.length) { // 添加数据并排序
                        let result = this.chartData.concat(list)
                        // 按工序顺序排序
                        this.chartData = result.sort((first, second) => first['seq'] - second['seq'])
                    }
                }
                // 根据设备信息，设置时间轴起止时间
                if (this.chartData.length) {
                    this.setTimeRange(this.chartData)
                }
                // 触发条状图重新渲染
                this.setZoom({ start: 0, end: 100 })
            }, err => {
                Loading.hide()
                this.showNotify({
                    message: err.message,
                    color: 'red'
                })
            })
        },
        setTimeRange (list = []) { // 从list中找出最大的时间范围,用于时序图的时间轴
            let timeMin = '', timeMax = ''
            list.forEach(_ => {
                if (_['status'] && _['status'].length) {
                    const firstT = _['status'][0]['time']
                    const lastT = _['status'][_['status'].length - 1]['time']
                    if (!timeMin || new Date(firstT) < new Date(timeMin)) {
                        timeMin = firstT
                    }
                    if (!timeMax || new Date(lastT) > new Date(timeMax)) {
                        timeMax = lastT
                    }
                }
            })
            if (timeMin && timeMax) {
                this.timeRange = [timeMin, timeMax]
                this.datazoomItem = {
                    data: this.timeRange
                }

                // 遍历设备,起止时间点若不是上面计算出的极限值的,将极限值加入
                list.forEach(_ => {
                    if (_['status'].length) {
                        // mark: 标记手动加入的数据,便于及时删除
                        _['status'][0].time > timeMin && (_['status'].unshift({
                            value: -1,
                            time: timeMin,
                            mark: true
                        }))
                        _['status'][_['status'].length - 1].time < timeMax && (_['status'].push({
                            value: -1,
                            time: timeMax,
                            mark: true
                        }))
                    }
                })

            }
        },
        setZoom (param) {
            this.datazoomObj = {
                start: param.start,
                end: param.end
            }
        },
        setPipelineAndDevice (workshops = []) { // list转为map结构
            if (!workshops.length) {
                this.workshopList = []
                this.pipelineMap = {}
                this.deviceMap = {}
                return
            }

            let workshopList = []
            this.workshopList = workshopList
            let pipelineMap = {}, deviceMap = {}
            workshops.forEach(item => {
                workshopList.push({ label: item['label'], value: item['value'] })
                const pipelines = item['pipeline_list'] || []
                // 只保存label和value字段，防止冗余数据占用内存
                let tempList = []
                pipelineMap[item['value']] = tempList
                if (pipelines.length) {
                    pipelines.forEach(obj => {
                        tempList.push({ label: obj['label'], value: obj['value'] })
                        deviceMap[item['value'] + '_' + obj['value']] = obj['device_list'] || []
                    })
                }
            })
            this.pipelineMap = pipelineMap
            this.deviceMap = deviceMap
        }
    }
}
</script>

<style lang="stylus" scoped>
.fixed-bar
    position fixed
    top 50px
    background #eee
    z-index 99
    .legend-container
      margin-top 2px
      .bar-legend
        box-sizing content-box
        border-width 2px
        font-size 12px
        color #fff
        padding 0 8px
        height 28px
        line-height 28px
        text-align center
        border-radius 4px
        margin-right 16px
        &.legend-selected
          box-shadow: 0 0 2px 2px #8a6363
        &.status-7
          color #7d7d7d !important
.chart-body
    position relative
    top 92px
.form-comp
    width 140px
</style>
<style lang="stylus">
.q-if.row.no-wrap.relative-position.q-input.q-if-has-label.q-if-standard.text-primary
    padding-top 0 !important
</style>
