<template>
<div class="row">
    <div class="column justify-center cursor-pointer" @click="deviceNameClick">
        <div class="text-center text-primary ng-sum">{{ ngSummaryNumber }}</div>
        <div class="device column justify-center items-center" :title="data.name">
            <div class="name text-primary">{{ data.name }}</div>
        </div>
    </div>

    <div class="col relative-position">
        <mg-echarts type="time-seq"
                    :data="chartData"
                    :events="ngEvents"
                    :show-x="false"
                    chartType2="production_amount"
                    width="100%" height="40px"></mg-echarts>
        <mg-echarts type="switch"
                    :data="chartData2"
                    :events="events"
                    width="100%" height="50px"></mg-echarts>

        <div class="absolute full-width text-white tooltip-exception"
             v-show="show && exceptionList.length" :style="{ top: this.customTop }">
            <div class="custom-tooltip shadow-3 q-pa-sm" :style="tooltipStyle">
                <div class="font-16" v-for="item in exceptionList" :key="item">{{ item }}</div>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import http from 'http/device'
import { paColors, type2color2, deviceType2name, defaultType, httpType } from 'assets/constant'
export default {
    props: {
        data: {
            type: Object,
            default: () => {}
        },
        zoom: {
            type: Object,
            default: () => ({
                start: 0,
                end: 100
            })
        },
        timeRange: { // x轴开始时间和结束时间
            type: Array,
            default: () => []
        },
        start: String, // 时间轴拖拽的时间起点
        end: String, // 时间轴拖拽的时间终点
        total: Number // 设备当前时段产量总数
    },
    computed: {
        chartData () {
            let reason2index = this.reason2index
            return {
                ...this.data,
                reason2index, // 原因分类对应的索引,便于在时序图中从颜色列表colors上取出对应颜色
                colors: paColors,
                zoom: this.zoom,
                timeRange: this.timeRange
            }
        },
        reasons () {
            return this.data.reasons || []
        },
        ngSummaryNumber () { // 不良数/生产数,不良率
            if (!this.total || !this.ngTotal) {
                return ''
            }
            let rate = this.ngTotal / this.total * 100
            rate = Math.round(rate * 100) / 100 // 保留2位有效数字
            return `${this.total}`
        },
        chartData2 () {
            return {
                type2color: type2color2,
                zoom: this.zoom,
                data: this.data.status,
                types: this.types
            }
        },
        // legend () {
        //     const list = this.data.status || []
        //     let result = []
        //     // 最后一个数据点的类型不需要关注
        //     list.slice(0, list.length - 1).forEach(_ => (!result.includes(_['value']) && result.push(_['value'])))
        //     result.sort()
        //     return result
        // },
        tooltipStyle () {
            return {
                width: this.customWidth,
                'box-sizing': 'content-box'
            }
        },
        deviceCode () {
            return this.data.code
        },
        userName () {
            return this.$q.cookies.get('login_name') || ''
        },
        isAdmin () {
            return ['root', 'admin'].includes(this.userName)
        }
    },
    data () {
        return {
            paColors,
            type2color2,
            types: [],
            legends: [], // 图例,显示当前时段内的不良品数量
            deviceName: '', // 设备
            ngTotal: 0, // 不良品总数
            reason2index: {
                '-1': 0,
                '1': 1,
                '2': 2,
                '3': 3,
                '4': 4,
                '5': 5,
                '6': 6
            },
            delay: 300,
            timeoutId: null,
            show: false, // hover事件,记录当前是否可以显示异常提示
            exceptionList: [],
            events: {
                mouseout: this.mouseout,
                mouseover: this.mouseover
            },
            ngEvents: {
                click: this.chartClick
            },
            customWidth: '200px',
            customTop: '60px',
            dataZero: true
        }
    },
    watch: {
        start (value) {
            this.setNgNumbers(value, this.end)
        },
        end (value) {
            this.setNgNumbers(this.start, value)
        },
        'data.data' () {
            this.setNgNumbers(this.start, this.end)
        },
        'data.types' () { // 选中的图例有变化时更新(设备去勾选时触发)
            this.types = this.data.types || []
        }
    },
    created () {
        this.types = this.data.types || []
        this.setNgNumbers(this.start, this.end)
    },
    methods: {
        deviceNameClick () {
            this.isAdmin && this.$emit('chart-click', this.data)
        },
        chartClick (param) {
            this.userName === 'admin' && this.$emit('chart-click', this.data, param)
        },
        selectStatus (type) { // 选中图例时触发条状图渲染（mg-echarts-switch中监听types变化）
            if (!this.types.includes(type)) {
                this.types.push(type)
            } else {
                this.types.splice(this.types.indexOf(type), 1)
            }
        },
        setNgNumbers (start, end) { // 计算start到end时间段内的不良品总数及
            const list = this.data.data || []
            let reasonMap = {}
            list.forEach(_ => {
                if (_['time'] >= start && _['time'] <= end) {
                    _['value'] in reasonMap ? (reasonMap[_['value']] += _['count']) : (reasonMap[_['value']] = _['count'])
                }
            })
            let result = []
            this.reasons.forEach(r => {
                const tempItem = r['value'] in reasonMap
                    ? { value: r['value'], label: r['label'], count: reasonMap[r['value']] }
                    : null
                tempItem && result.push(tempItem)
            })
            this.legends = result
            this.ngTotal = result.reduce((a, b) => a + b['count'], 0)
        },
        mouseout () {
            this.timeoutId && clearTimeout(this.timeoutId)
            this.show = false
            this.exceptionList = []
        },
        mouseover (param) {
            this.show = true
            this.debounce(param.data)
        },
        debounce (data) {
            this.timeoutId && clearTimeout(this.timeoutId)
            this.timeoutId = setTimeout(() => {
                this.getDeviceException(data)
            }, this.delay)
        },
        getDeviceException (item) {
            const type = item[2]
            if (defaultType.includes(type) && this.show) { // tooltip内容来自type2name变量
                const duration = (new Date(item[1]) - new Date(item[0])) / 1000
                this.setDeviceTooltip([`${deviceType2name[type]} 持续 ${this.getTimeStr(duration)}`])
            } else if (httpType.includes(type)) { // tooltip内容来自http返回数据
                // 在请求发起之前设置show，防止多次mouseover执行而请求结果延时导致显示的异常信息不匹配
                http.getDeviceException({ 'device_code': this.deviceCode, start_time: item[0], end_time: item[1] }, function (res) {
                    if (!this.show) {
                        this.exceptionList = []
                        return
                    }
                    if (res.code === 'success' && res.data && res.data.length) {
                        this.setDeviceTooltip(res.data)
                    } else {
                        this.exceptionList = []
                    }
                }.bind(this))
            } else {
                this.exceptionList = []
            }
        },
        getTimeStr (duration) { // 将 '秒' 转换成 'hh:mm:ss' 格式
            let hours = Math.floor(duration / 3600) // 小时
            hours = hours < 10 ? `0${hours}` : hours
            const lastSecs = duration % 3600
            let minutes = Math.floor(lastSecs / 60) // 分钟
            minutes = minutes < 10 ? `0${minutes}` : minutes
            let seconds = lastSecs % 60
            seconds = seconds < 10 ? `0${seconds}` : seconds
            return `${hours}:${minutes}:${seconds}`
        },
        setDeviceTooltip (list) {
            this.exceptionList = list
            const len = list.length
            if (!len) {
                return
            }
            let wordNum = 0
            list.forEach(item => (wordNum = Math.max(wordNum, item.length)))
            this.customWidth = wordNum * 18 + 'px'
            if (len > 2) {
                this.customTop = this.dataZero ? '60px' : (200 + (len * -16)) + 'px'
            } else {
                this.customTop = '60px'
            }
        }
    }
}
</script>

<style lang="stylus" scoped>
@import "~quasar-variables"
.ng-sum
    font-weight 700
.device
  border-radius 4px
  font-size 13px
  width 80px
  height 42px
  padding 2px 4px
  .name
    overflow hidden
    -webkit-line-clamp 1
    text-overflow ellipsis
    -webkit-box-orient vertical
    display -webkit-box

.device-icon
    background $primary
    border-radius 5px
    padding 3px

.bar-legend
    width 20px
    height 14px
    border-radius 4px

.legend
    border-width 2px
    border-style dashed
    border-color transparent

.legend-selected
    border-color #8a6363
    border-radius 4px

.margin-right
    margin-right 4px

.font-14
    font-size 14px
</style>
<style lang="stylus" scoped>
.status-chart
  position relative
  top -20px
.custom-tooltip
  background-color #747474
  color #fafafa
  margin 0 auto
  border-radius 4px
.tooltip-exception
  z-index 100
  /*top 10px*/
</style>
