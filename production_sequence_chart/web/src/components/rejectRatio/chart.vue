<template>
    <div class="relative-position overflow-hidden" :style="{width: width, height: height}">
        <div v-show="!isChart" class="full-width full-height text-center no-data">
          暂无数据
        </div>
        <div class="mark-legend absolute">
            <template v-for="legend in threshold">
                <span class="q-ml-sm"
                :key="`${legend.type}${legend.value}`"
                :style="{
                    color: markColors[legend.type]
                }"><span class="mark-legend-line" :style="{
                    'border-color': markColors[legend.type]
                }" />{{legend.type}}({{legend.value}})</span>
            </template>
        </div>
        <div :ref="refName" class="full-width full-height absolute" />
   </div>
</template>

<script>
import echarts from 'echarts'
import { uid } from 'quasar'
import { markColors } from 'assets/constant'
import { initEvent } from 'assets/untils'

export default {
    props: {
        width: { // 图表宽度
            type: String,
            default: '100%'
        },
        height: { // 图表高度
            type: String,
            default: '100%'
        },
        data: {
            type: Array,
            default: () => []
        },
        events: {
            type: Object,
            default: () => {}
        },
        threshold: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        data: {
            immediate: true,
            handler () {
                this.initChart()
            }
        },
        width () {
            this.resizeChart()
        },
        height () {
            this.resizeChart()
        }
    },
    computed: {
        isChart () {
            return this.data && this.data.length
        }
    },
    data () {
        return {
            markColors,
            timer: null,
            chart: null,
            left: 20,
            right: 20,
            refName: uid()
        }
    },
    mounted () {
        this.initChart()
        window.addEventListener('resize', this.resizeChart)
    },
    beforeDestroy () {
        this.clearChart()
    },
    destroyed () {
        window.removeEventListener('resize', this.resizeChart)
    },
    methods: {
        debounce () {
            this.timer && clearTimeout(this.timer)
            this.startTimeout()
        },
        startTimeout () {
            this.timer = setTimeout(function () {
                this.chart && this.setOption()
            }.bind(this), 100)
        },
        timeLabel (value) {
            let [day, time] = value.split(' ')
            return `${day} \n ${time}`
        },
        setOption () {
            if (!this.data) {
                return
            }
            let markLine = [] // 标记线
            this.threshold.forEach(v => {
                let { value, type } = v
                markLine.push({ yAxis: value, lineStyle: { color: this.markColors[type] } })
            })

            const extralOpt = {
                color: ['#61a0a8'],
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        let { value } = params[0]
                        return `不良率: ${value[1]}%</br>
                                上报时间: ${value[0]}`
                    }
                },
                // 时间轴可拖拽
                dataZoom: [{
                    type: 'slider',
                    filterMode: 'filter',
                    showDataShadow: false,
                    bottom: 6,
                    height: 10,
                    borderColor: 'transparent',
                    backgroundColor: '#e2e2e2',
                    handleIcon: 'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z', // jshint ignore:line
                    handleSize: 20,
                    handleStyle: {
                        shadowBlur: 6,
                        shadowOffsetX: 1,
                        shadowOffsetY: 2,
                        shadowColor: '#aaa'
                    }
                }],
                title: {
                    show: false
                },
                grid: {
                    left: this.left,
                    right: this.right,
                    bottom: 50,
                    top: 20,
                    borderWidth: 0
                },
                xAxis: {
                    type: 'time',
                    splitNumber: 24,
                    axisTick: {
                        length: 3,
                        lineStyle: {
                            color: '#AFADAD',
                            width: 2
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '%',
                    nameTextStyle: {
                        padding: -10
                    },
                    axisLabel: {
                        inside: true
                    }
                },
                series: {
                    type: 'line',
                    data: this.data.map(v => [v.time, v.value]),
                    markLine: {
                        silent: true,
                        data: markLine
                    }
                }
            }

            if (!this.chart.getOption()) {
                this.chart.setOption(extralOpt)
            } else {
                this.chart.setOption(extralOpt)
            }
        },
        initChart () {
            if (this.isChart) {
                if (!this.chart && this.$refs[this.refName]) {
                    this.chart = echarts.init(this.$refs[this.refName])
                    initEvent(this.events, this.chart)
                }
                // this.chart.showLoading()
                this.debounce()
            } else {
                this.clearChart()
            }
        },
        clearChart () {
            if (!this.chart) {
                return
            }

            this.chart.clear()
            this.chart.dispose()
            this.chart = null
        },
        resizeChart () {
            this.$nextTick(function () {
                this.chart && this.chart.resize()
            })
        }
    }
}
</script>
<style lang="stylus" scoped>
  .no-data
    color grey
  .mark-legend
    right 0
    top 2px
    font-size 14px
    .mark-legend-line
        display inline-block
        height 0
        width 20px
        border-bottom 2px dashed #f00
</style>
