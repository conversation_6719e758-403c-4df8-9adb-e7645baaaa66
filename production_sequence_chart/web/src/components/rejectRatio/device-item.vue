<template>
  <div>
    <div class="row justify-between q-mt-xs q-px-xs">
      <div>
          <span class="device-icon inline-block">
            <q-icon name="settings_input_hdmi" size="14px" color="white"/>
          </span>
          <span class="q-ml-xs vertical-middle text-primary text-bold cursor-pointer font-16">{{this.data.name}}{{ngSummaryNumber}}</span>
      </div>
      <!-- 原因图例 -->
      <div class="row">
        <template v-for="(r, i) in legends">
          <div v-if="r" :key="`legend_${i}`"
               class="q-mr-xs cursor-pointer row items-center legend"
               :class="{'legend-selected' : (types.includes(r.value))}"
               @click="selectStatus(r.value)">
              <div class="bar-legend margin-right"
                   :style="{ 'background-color': ngColors[reason2index[r.value]] }"></div>
              <div class="font-14">{{ r.label }}({{ r.count }})</div>
          </div>
        </template>
      </div>
    </div>
    <div class="q-px-md">
        <div v-if="rateData.length || ngData.length">
            <!-- 不良品数据 -->
            <mg-echarts type="time-seq"
                        :show-x="false"
                        :data="chartData"
                        :events="rejectEvents"
                        width="100%" height="40px" />
            <!-- 不良率数据 -->
            <chart width="100%" height="180px" :threshold="threshold"
               :data="rateData" :events="events" />
        </div>
        <div v-else class="full-width full-height text-center no-data">
          暂无数据
        </div>
    </div>
  </div>
</template>

<script>
import { ngColors } from 'assets/constant'
import { openNewUrl } from 'assets/untils'
import { mapState } from 'vuex'
import { date } from 'quasar'
import chart from './chart'
import http from 'http/reject'
export default {
    components: { chart },
    props: {
        data: {
            type: Object,
            default: () => {}
        },
        reason2index: {
            type: Object,
            default: () => {}
        }
    },
    computed: {
        ...mapState(['organization']),
        reasons () {
            return this.data.reasons || []
        },
        rateData () {
            return this.data.rate_data || []
        },
        ngData () {
            return this.data.ng_data || []
        },
        threshold () {
            return this.data.threshold || []
        },
        timeRange () {
            let len = this.rateData.length
            let nglen = this.ngData.length
            return len ? [this.rateData[0].time, this.rateData[len - 1].time] : nglen ? [this.ngData[0].time, this.ngData[nglen - 1].time] : []
        },
        ngSummaryNumber () { // 不良数/生产数,不良率
            if (!this.total) { //  || !this.ngTotal
                return ''
            }
            let optimizedNgTotal = this.ngTotal
            if(this.ngTotal > this.total) {
                optimizedNgTotal = this.total
            }
            let rate = optimizedNgTotal / this.total * 100
            rate = Math.round(rate * 100) / 100 // 保留2位有效数字
            return `(${optimizedNgTotal}/${this.total}, ${rate}%)`
        },
        chartData () {
            let reason2index = {}
            this.reasons.forEach((_, i) => {
                reason2index[_['value']] = i
            })
            return {
                ...this.data,
                data: this.ngData,
                reason2index: this.reason2index, // 原因分类对应的索引,便于在时序图中从颜色列表colors上取出对应颜色
                colors: ngColors,
                zoom: this.zoom,
                timeRange: this.timeRange
            }
        },
        isAdmin () {
            return this.$q.cookies.has('login_name') && ['root', 'admin'].includes(this.$q.cookies.get('login_name'))
        }
    },
    data () {
        return {
            timer: null,
            timeout: 100,

            ngColors,
            zoom: {
                start: 0,
                end: 100
            },

            clickTimes: 0, // 点击次数,防止双击同时触发了单击
            timeId: null,
            rejectEvents: {
                click: this.chartClick,
                dblclick: this.chartClick
            },
            events: { // 时间轴事件
                datazoom: this.setZoom
            },
            total: 0, // 总个数
            ngTotal: 0, // 不良品总数

            types: [],
            legends: [], // 图例,显示当前时段内的不良品数量

            start: 0,
            end: 100
        }
    },
    watch: {
        start (value) {
            value && this.setNgNumbers(value, this.end)
        },
        end (value) {
            value && this.setNgNumbers(this.start, value)
        },
        'timeRange' () { // 'data.ng_data'
            this.getValue()
        },
        'data.types' () { // 选中的图例有变化时更新(设备去勾选时触发)
            this.types = this.data.types || []
        },
        chartData (value) {
            this.start && this.end && this.setNgNumbers(this.start, this.end)
        }
    },
    created () {
        this.types = this.data.types || []
        this.setZoom({ start: 0, end: 100 })
    },
    beforeDestroy () {
        this.timeId && this.clearTime()
    },
    methods: {
        chartClick (param) {
            ++this.clickTimes
            this.timeId && this.clearTime()

            this.timeId = setTimeout(() => {
                this.clearTime()
                if (this.clickTimes === 1) {
                    this.clickHandler(param)
                } else {
                    this.dblclickHandler(param)
                }
                this.clickTimes = 0
            }, 200)
        },
        clearTime () {
            clearTimeout(this.timeId)
            this.timeId = null
        },
        dblclickHandler (param) { // 双击,root或admin账户可修改数据
            this.isAdmin && this.$emit('chart-click', 'edit', this.data, param)
        },
        clickHandler (param) { // 单击,跳转到根因分析app
            const data = param.data || []
            const params = {
                device_code: this.data.code,
                unqualified_reason_name: data[2],
                time: data[0],
                from: 'board'
            }
            openNewUrl(this.$router, '/analysis', this.organization + '/unqualified_reason_analysis', params)
        },
        selectStatus (type) { // 选中图例时触发条状图渲染（mg-echarts-switch中监听types变化）
            if (!this.types.includes(type)) {
                this.types.push(type)
            } else {
                this.types.splice(this.types.indexOf(type), 1)
            }
        },
        setNgNumbers (start, end) { // 计算start到end时间段内的不良品总数及
            const list = this.ngData || []
            let reasonMap = {}
            list.forEach(_ => {
                if (_['time'] >= start && _['time'] <= end) {
                    _['value'] in reasonMap ? (reasonMap[_['value']] += _['count']) : (reasonMap[_['value']] = _['count'])
                }
            })
            let result = []
            this.reasons.forEach(r => {
                const tempItem = r['value'] in reasonMap
                    ? { value: r['value'], label: r['label'], count: reasonMap[r['value']] }
                    : null
                result.push(tempItem)
            })
            this.legends = result
            this.ngTotal = result.reduce((a, b) => a + b['count'], 0)
        },
        setZoom (param) {
            this.zoom = {
                start: param.start,
                end: param.end
            }
            this.timer && clearTimeout(this.timer)
            this.timer = setTimeout(function () {
                this.getValue()
            }.bind(this), this.timeout)
        },
        getValue () { // 获取当前时间之内的数据统计
            // zoom变化 触发事件
            // 解析出时间点
            const startTimestamp = new Date(this.timeRange[0]).getTime()
            const endTimestamp = new Date(this.timeRange[1]).getTime()
            const timestampGap = endTimestamp - startTimestamp
            const startPercent = this.zoom.start || 0
            const endPercent = this.zoom.end || 100
            const start = startTimestamp + (startPercent * timestampGap) / 100
            const end = startTimestamp + (endPercent * timestampGap) / 100
            this.start = date.formatDate(start, 'YYYY-MM-DD HH:mm:ss')
            this.end = date.formatDate(end, 'YYYY-MM-DD HH:mm:ss')

            const param = {
                device_code_list: [this.data.code],
                timestamp: [this.start, this.end]
            }
            // 获取当前的总数量
            http.getProductCapacity(param, res => {
                if (this.responseValidate(res.data)) {
                    this.total = res.data ? res.data[this.data.code] : 0
                }
            })
        }
    }
}
</script>

<style lang="stylus" scoped>
  @import "~quasar-variables"
  .device-icon
    background $primary
    border-radius 5px
    padding 3px

  .bar-legend
    width 20px
    height 14px
    border-radius 4px

  .legend
    border-width 2px
    border-style dashed
    border-color transparent

  .legend-selected
    border-color #8a6363
    border-radius 4px

  .margin-right
    margin-right 4px

  .font-14
    font-size 14px
</style>
