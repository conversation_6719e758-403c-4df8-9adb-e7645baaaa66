import Vue from 'vue'
import Vuex, { Module, Store } from 'vuex'
import * as actions from './actions'
import * as getters from './getters'
import state from './state'
import * as mutations from './mutations'

// 从framework读取store
import { store } from 'mg-front-end-framework'
let mgStore: (Module<MgState, MgState>) | Store<MgState> = {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}

if (!Vue.prototype.isPortal) {
    Vue.use(Vuex)
    mgStore = new Vuex.Store({
        ...store,
        modules: { [process.env.APP_SCOPE_NAME as string]: mgStore },
        strict: process.env.NODE_ENV === 'development'
    })
}

export default mgStore
