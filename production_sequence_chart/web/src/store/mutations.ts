import { setThumbnailName } from 'assets/untils'

// 设置页面title显示
export const setPageTitle = (state: MgState, title: string): void => {
    state.pageTitle = title
    setThumbnailName(title)
}

export const toggleHeader = (state: MgState, status: boolean): void => {
    state.headerStatus = status
}

export const setDeviceTree = (state: MgState, list: any): void => {
    state.deviceTree = list || []
}

// 角色校验配置更新
export const setRoleValidate = (state, data) => {
    const keys = Object.keys(state.roleValidateConfig)
    keys.forEach(k => {
        if (k === 'show') {
            state.roleValidateConfig[k] = true
        } else if (k in data) {
            state.roleValidateConfig[k] = data[k]
        } else {
            state.roleValidateConfig[k] = null
        }
    })
}
// 关闭权限验证
export const closeRoleValidate = (state) => {
    state.roleValidateConfig.show = false
}
