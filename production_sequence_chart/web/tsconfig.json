{"compilerOptions": {"skipLibCheck": true, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "resolveJsonModule": true, "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "noImplicitAny": false, "allowJs": true, "baseUrl": "./", "outDir": "./dist", "rootDir": "./src", "paths": {"assets/*": ["src/statics/assets/*"], "http/*": ["src/api/http/*"], "components/*": ["src/components/*"], "*": ["types/*"], "@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/*.vue", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["lib/*", "node_modules", "./dist", "src/**/*.js", "src/**/*.jsx", "tests/**/*.js", "tests/**/*.jsx"]}