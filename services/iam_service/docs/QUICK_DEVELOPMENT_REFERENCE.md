# IAM服务开发快速参考

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 12+
- Redis 6+

### 快速启动
```bash
# 后端
cd services/iam_service
pip install -r requirements.txt
python main.py

# 前端
cd services/iam_service/frontend
npm install
npm run dev
```

## 📋 开发优先级

### 🔴 第一优先级 (必须先完成)
1. **租户管理** - 其他模块的基础
2. **用户管理** - 认证的基础
3. **认证安全** - 系统入口

### 🟡 第二优先级 (核心功能)
4. **角色管理** - 权限基础
5. **权限管理** - 权限控制
6. **RBAC系统** - 权限核心

### 🟢 第三优先级 (增强功能)
7. **审计日志** - 合规要求
8. **系统配置** - 系统管理
9. **高级安全** - 安全增强

## 🔧 后端开发顺序

### 第1周：基础设施
```python
# 1. 数据库模型
class Tenant(Base):
    __tablename__ = "tenants"
    tenant_id = Column(UUID, primary_key=True)
    tenant_name = Column(String(100), nullable=False)
    # ...

# 2. 服务层
class TenantService:
    async def create_tenant(self, **kwargs):
        # 实现逻辑
        pass

# 3. 路由层
@router.post("/tenants/create")
async def create_tenant(request: CreateTenantRequest):
    # 路由实现
    pass
```

### 第2-3周：核心模块
**开发顺序**:
1. 租户管理 → 用户管理 → 认证安全
2. 每个模块：模型 → 服务 → 路由 → 测试

### 第4-5周：权限模块
**开发顺序**:
1. 角色管理 → 权限管理 → RBAC集成
2. 权限检查 → 权限缓存 → 权限继承

### 第6周：高级功能
**开发顺序**:
1. 审计日志 → 系统配置 → 高级安全
2. 性能优化 → 监控集成

## 🎨 前端开发顺序

### 第1周：项目搭建
```typescript
// 1. 基础组件
export const Button: React.FC<ButtonProps> = ({ ... }) => {
  return <button className={cn(baseStyles, variants[variant])} {...props} />
}

// 2. 布局组件
export const Layout: React.FC = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <Sidebar />
      <main>{children}</main>
    </div>
  )
}
```

### 第2周：认证模块
**页面开发顺序**:
1. 登录页面 → 注册页面 → 密码重置
2. MFA设置 → 会话管理

### 第3-4周：业务页面
**页面开发顺序**:
1. 用户管理 → 角色管理 → 权限管理
2. 租户管理 → 审计日志

### 第5周：高级功能
**页面开发顺序**:
1. 系统配置 → 安全管理 → 仪表板

## 🔑 关键API接口

### 认证相关
```bash
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新令牌
POST /api/v1/auth/change-password # 修改密码
```

### 用户管理
```bash
POST /api/v1/users/create        # 创建用户
POST /api/v1/users/query         # 查询用户列表
POST /api/v1/users/detail        # 获取用户详情
POST /api/v1/users/update        # 更新用户信息
```

### 角色权限
```bash
POST /api/v1/roles/create        # 创建角色
POST /api/v1/rbac/users/roles/assign    # 分配用户角色
POST /api/v1/rbac/permissions/check     # 权限检查
```

### 租户管理
```bash
POST /api/v1/tenants/create      # 创建租户
POST /api/v1/tenants/query       # 查询租户列表
POST /api/v1/tenants/detail      # 获取租户详情
```

## 🛡️ 权限控制实现

### 后端权限检查
```python
from functools import wraps

def require_permissions(permissions: List[str]):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 权限检查逻辑
            current_user = kwargs.get('current_user')
            user_permissions = await get_user_permissions(current_user.user_id)
            
            if not all(perm in user_permissions for perm in permissions):
                raise HTTPException(status_code=403, detail="权限不足")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@router.post("/users/create")
@require_permissions(["user:create"])
async def create_user(request: CreateUserRequest):
    pass
```

### 前端权限控制
```typescript
// 权限守卫组件
export const PermissionGuard: React.FC<{
  permissions: string[]
  children: React.ReactNode
}> = ({ permissions, children }) => {
  const { hasPermissions } = useAuth()
  
  if (!hasPermissions(permissions)) {
    return null
  }
  
  return <>{children}</>
}

// 使用示例
<PermissionGuard permissions={['user:create']}>
  <CreateUserButton />
</PermissionGuard>
```

## 📊 数据模型关系

### 核心实体关系
```
Tenant (租户)
  ├── Users (用户)
  ├── Roles (角色)
  └── Permissions (权限)

User (用户)
  ├── UserRoles (用户角色关联)
  ├── Sessions (会话)
  └── AuditLogs (审计日志)

Role (角色)
  ├── RolePermissions (角色权限关联)
  ├── UserRoles (用户角色关联)
  └── ParentRole (父角色)
```

### 权限继承逻辑
```
用户有效权限 = 直接分配权限 + 角色权限 + 继承权限
角色权限 = 直接分配权限 + 父角色权限
```

## 🔧 开发工具配置

### VSCode 配置
```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true
}
```

### Git 提交规范
```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建过程或辅助工具变动
```

## 🧪 测试策略

### 后端测试
```python
# 单元测试示例
import pytest
from services.user_service import UserService

@pytest.mark.asyncio
async def test_create_user():
    user_service = UserService()
    result = await user_service.create_user(
        tenant_id="test-tenant",
        username="testuser",
        email="<EMAIL>",
        password="password123"
    )
    assert result.username == "testuser"
```

### 前端测试
```typescript
// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from './Button'

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

## 🚀 部署配置

### Docker 快速部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: iam_db
      POSTGRES_USER: iam_user
      POSTGRES_PASSWORD: iam_password
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  iam-backend:
    build: .
    environment:
      DATABASE_URL: ************************************************/iam_db
      REDIS_URL: redis://redis:6379
    ports:
      - "8089:8089"
    depends_on:
      - postgres
      - redis
```

### 环境变量配置
```bash
# .env
DATABASE_URL=postgresql://user:password@localhost:5432/iam_db
REDIS_URL=redis://localhost:6379
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
```

## 📋 开发检查清单

### 每日检查
- [ ] 代码提交前运行测试
- [ ] 代码格式化检查
- [ ] API 文档更新
- [ ] 错误处理完善

### 每周检查
- [ ] 性能测试运行
- [ ] 安全扫描检查
- [ ] 依赖更新检查
- [ ] 代码覆盖率检查

### 发布前检查
- [ ] 所有测试通过
- [ ] 文档完整更新
- [ ] 安全审计通过
- [ ] 性能基准达标

## 🆘 常见问题

### 数据库连接问题
```bash
# 检查连接
psql -h localhost -U iam_user -d iam_db

# 重置连接池
SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'iam_db';
```

### Redis 连接问题
```bash
# 检查连接
redis-cli ping

# 清空缓存
redis-cli flushall
```

### 权限问题排查
```python
# 调试用户权限
async def debug_permissions(user_id: str):
    user_roles = await get_user_roles(user_id)
    permissions = await get_user_permissions(user_id)
    return {
        "roles": [r.role_name for r in user_roles],
        "permissions": [p.permission_code for p in permissions]
    }
```

### 前端构建问题
```bash
# 清理缓存
rm -rf node_modules package-lock.json
npm install

# 类型检查
npm run type-check

# 构建检查
npm run build
```

## 📞 技术支持

### 文档链接
- [完整开发指南](./DEVELOPMENT_ORDER_GUIDE.md)
- [组件开发规范](../frontend/COMPONENT_DEVELOPMENT_STANDARDS.md)
- [API 文档](http://localhost:8089/docs)

### 联系方式
- 技术问题：提交 Issue
- 紧急问题：联系项目负责人
- 文档问题：提交 PR

---

**提示**: 这是快速参考指南，详细信息请查看完整的开发指南文档。
