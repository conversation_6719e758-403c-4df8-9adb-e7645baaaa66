/**
 * @file 加载动画组件
 * @description 提供各种加载状态的动画组件
 * @status 开发中
 */

import React from 'react'
import { cn } from '@/utils/common'

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'spinner' | 'dots' | 'pulse'
  className?: string
  text?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'spinner',
  className,
  text
}) => {
  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  }

  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }

  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <svg
            className={cn('animate-spin', sizes[size])}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )

      case 'dots':
        return (
          <div className="flex space-x-1">
            <div className={cn('bg-current rounded-full animate-pulse', 
              size === 'sm' ? 'h-1 w-1' : 
              size === 'md' ? 'h-2 w-2' : 
              size === 'lg' ? 'h-3 w-3' : 'h-4 w-4'
            )} style={{ animationDelay: '0ms' }} />
            <div className={cn('bg-current rounded-full animate-pulse', 
              size === 'sm' ? 'h-1 w-1' : 
              size === 'md' ? 'h-2 w-2' : 
              size === 'lg' ? 'h-3 w-3' : 'h-4 w-4'
            )} style={{ animationDelay: '150ms' }} />
            <div className={cn('bg-current rounded-full animate-pulse', 
              size === 'sm' ? 'h-1 w-1' : 
              size === 'md' ? 'h-2 w-2' : 
              size === 'lg' ? 'h-3 w-3' : 'h-4 w-4'
            )} style={{ animationDelay: '300ms' }} />
          </div>
        )

      case 'pulse':
        return (
          <div className={cn('bg-current rounded-full animate-pulse opacity-75', sizes[size])} />
        )

      default:
        return null
    }
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex flex-col items-center space-y-2">
        <div className="text-gray-500">
          {renderSpinner()}
        </div>
        {text && (
          <p className={cn('text-gray-500', textSizes[size])}>
            {text}
          </p>
        )}
      </div>
    </div>
  )
}

// 全屏加载组件
export const FullScreenLoading: React.FC<{ text?: string }> = ({ text = '加载中...' }) => {
  return (
    <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <LoadingSpinner size="lg" text={text} />
    </div>
  )
}

// 页面加载组件
export const PageLoading: React.FC<{ text?: string }> = ({ text = '加载中...' }) => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <LoadingSpinner size="lg" text={text} />
    </div>
  )
}

// 内容加载组件
export const ContentLoading: React.FC<{ text?: string; className?: string }> = ({ 
  text = '加载中...', 
  className 
}) => {
  return (
    <div className={cn('flex items-center justify-center py-12', className)}>
      <LoadingSpinner size="md" text={text} />
    </div>
  )
}

// 按钮加载组件
export const ButtonLoading: React.FC = () => {
  return <LoadingSpinner size="sm" variant="spinner" />
}

// 骨架屏组件
export const Skeleton: React.FC<{ 
  className?: string
  variant?: 'text' | 'rectangular' | 'circular'
}> = ({ className, variant = 'text' }) => {
  const variants = {
    text: 'h-4 rounded',
    rectangular: 'rounded',
    circular: 'rounded-full'
  }

  return (
    <div 
      className={cn(
        'bg-gray-200 animate-pulse',
        variants[variant],
        className
      )} 
    />
  )
}

// 卡片骨架屏
export const CardSkeleton: React.FC = () => {
  return (
    <div className="border border-gray-200 rounded-lg p-6 space-y-4">
      <Skeleton className="h-6 w-3/4" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
        <Skeleton className="h-4 w-4/6" />
      </div>
      <div className="flex space-x-2">
        <Skeleton className="h-8 w-20" variant="rectangular" />
        <Skeleton className="h-8 w-20" variant="rectangular" />
      </div>
    </div>
  )
}

// 表格骨架屏
export const TableSkeleton: React.FC<{ rows?: number; cols?: number }> = ({ 
  rows = 5, 
  cols = 4 
}) => {
  return (
    <div className="space-y-3">
      {/* 表头 */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>
        {Array.from({ length: cols }).map((_, index) => (
          <Skeleton key={index} className="h-6 w-full" />
        ))}
      </div>
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>
          {Array.from({ length: cols }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-4 w-full" />
          ))}
        </div>
      ))}
    </div>
  )
}

export { LoadingSpinner }
