.datasetWrapper {
  padding: 30px 30px 0;
  height: 100%;
}

.documentTable {
  tbody {
    // height: calc(100vh - 508px);
  }
}

.filter {
  height: 32px;
  display: flex;
  margin: 10px 0;
  justify-content: space-between;
  padding: 24px 0;
  align-items: center;
}

.deleteIconWrapper {
  width: 22px;
  text-align: center;
}

.img {
  height: 24px;
  width: 24px;
  display: inline-block;
  vertical-align: middle;
}

.column {
  min-width: 200px;
}

.toChunks {
  cursor: pointer;
}

.pageInputNumber {
  width: 220px;
}

.questionIcon {
  margin-inline-start: 4px;
  color: rgba(0, 0, 0, 0.45);
  cursor: help;
  writing-mode: horizontal-tb;
}

.nameText {
  color: #1677ff;
}
